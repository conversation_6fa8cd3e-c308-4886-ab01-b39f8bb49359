package org.tio.p000mg.web.server;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.mg.service.init.CacheInit;
import org.tio.mg.service.init.JFInit;
import org.tio.mg.service.init.JsonInit;
import org.tio.mg.service.init.PropInit;
import org.tio.mg.service.init.RedisInit;
import org.tio.mg.service.ip2region.Ip2RegionInit;
import org.tio.mg.service.service.base.SensitiveWordsService;
import org.tio.p000mg.web.server.init.TopicInit;
import org.tio.p000mg.web.server.init.WebApiInit;
import org.tio.utils.quartz.QuartzUtils;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/WebApiStarter.class */
public class WebApiStarter {
    private static Logger log = LoggerFactory.getLogger(WebApiStarter.class);

    public static void main(String[] args) throws Exception {
        try {
            PropInit.init();
            Ip2RegionInit.init();
            SensitiveWordsService.init();
            JsonInit.init();
            JFInit.init();
            CacheInit.init(true);
            RedisInit.init(true);
            TopicInit.init();
            WebApiInit.init();
            QuartzUtils.start();
        } catch (Throwable e) {
            log.error(e.toString(), e);
            System.exit(1);
        }
    }
}
