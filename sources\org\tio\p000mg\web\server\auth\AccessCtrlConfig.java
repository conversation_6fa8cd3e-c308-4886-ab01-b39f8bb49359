package org.tio.p000mg.web.server.auth;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.configuration2.PropertiesConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.utils.ConfigUtils;
import org.tio.utils.json.Json;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/auth/AccessCtrlConfig.class */
public class AccessCtrlConfig extends PropertiesConfiguration {
    private static Logger log = LoggerFactory.getLogger(AccessCtrlConfig.class);
    private Set<String> allRequestMapping;
    private boolean skipRepeat;
    private String configFile;

    public AccessCtrlConfig(String configFile, Set<String> allRequestMapping, boolean skipRepeat) {
        this.allRequestMapping = null;
        this.skipRepeat = false;
        this.configFile = "";
        this.skipRepeat = skipRepeat;
        if (allRequestMapping == null) {
            this.allRequestMapping = new HashSet();
        } else {
            this.allRequestMapping = new HashSet(allRequestMapping);
        }
        this.configFile = configFile;
        try {
            ConfigUtils.initConfig(configFile, "utf-8", this);
        } catch (FileNotFoundException e) {
            log.error(e.toString(), e);
        }
        Iterator<String> keys = getKeys();
        step1(keys);
        Iterator<String> keys2 = getKeys();
        Map<String, Object> map = step2(keys2);
        step3(map);
    }

    public Object getNeededRolecodes(String path) {
        Object neededRolecodes = getProperty(path);
        return neededRolecodes;
    }

    private void step1(Iterator<String> keys) {
        PropertiesConfiguration tem = new PropertiesConfiguration();
        while (keys.hasNext()) {
            String configedServletPath = keys.next();
            Object rolecodes = getProperty(configedServletPath);
            if (!configedServletPath.startsWith("/")) {
                configedServletPath = "/" + configedServletPath;
            }
            tem.addProperty(configedServletPath, rolecodes);
            if (!StrUtil.containsAny(configedServletPath, new CharSequence[]{"*"}) && !this.allRequestMapping.contains(configedServletPath)) {
                System.out.println(this.configFile + "没有提供：" + configedServletPath);
                this.allRequestMapping.add(configedServletPath);
            }
        }
        clear();
        append(tem);
    }

    private Map<String, Object> step2(Iterator<String> configServletPaths) {
        Map<String, Object> tem1;
        Map<String, Object> asteriskServletPath = new HashMap<>();
        Map<String, Object> notAsteriskServletPath = new HashMap<>();
        Set<String> noMatchServletPath = new HashSet<>();
        while (configServletPaths.hasNext()) {
            String configedServletPath = configServletPaths.next();
            Object rolecodes = getProperty(configedServletPath);
            if (configedServletPath.contains("*")) {
                tem1 = asteriskServletPath;
                configedServletPath = configedServletPath.replaceAll("\\*", ".*");
            } else {
                tem1 = notAsteriskServletPath;
            }
            boolean ismatched = false;
            for (String requestMapping : this.allRequestMapping) {
                Pattern p = Pattern.compile(configedServletPath);
                Matcher m = p.matcher(requestMapping);
                boolean b = m.matches();
                if (b) {
                    ismatched = true;
                    if (rolecodes instanceof List) {
                        List<String> rolecodeList = (List) rolecodes;
                        for (String rolecode : rolecodeList) {
                            addRoleToRequestmapping(requestMapping, rolecode, tem1);
                        }
                    } else {
                        addRoleToRequestmapping(requestMapping, (String) rolecodes, tem1);
                    }
                }
            }
            if (!ismatched) {
                noMatchServletPath.add(configedServletPath);
            }
        }
        if (noMatchServletPath.size() > 0) {
            log.error("有{}个配置项没有找到映射路径，请检查是否配置有误:{}", Integer.valueOf(noMatchServletPath.size()), Json.toJson(noMatchServletPath));
        }
        asteriskServletPath.putAll(notAsteriskServletPath);
        clear();
        Set<Map.Entry<String, Object>> set = asteriskServletPath.entrySet();
        for (Map.Entry<String, Object> entry : set) {
            addProperty(entry.getKey(), entry.getValue());
        }
        return asteriskServletPath;
    }

    private void step3(Map<String, Object> map) {
        try {
            String writeMappingToFile = System.getProperty("tio.mvc.route.writeMappingToFile", "true");
            if ("true".equalsIgnoreCase(writeMappingToFile)) {
                FileUtil.writeString(Json.toFormatedJson(map), new File("/" + FileUtil.mainName(this.configFile) + ".js"), "utf-8");
            }
        } catch (Throwable th) {
        }
    }

    private void addRoleToRequestmapping(String requestMapping, String rolecode, Map<String, Object> map) {
        Object _rcs = map.get(requestMapping);
        if (_rcs == null) {
            map.put(requestMapping, rolecode);
            return;
        }
        if (!this.skipRepeat) {
            log.error("配置项重复{}", requestMapping);
        }
        if (_rcs instanceof List) {
            List<String> rclist = (List) _rcs;
            if (!rclist.contains(rolecode)) {
                rclist.add(rolecode);
                map.put(requestMapping, rclist);
                return;
            }
            return;
        }
        if (!_rcs.equals(rolecode)) {
            List<String> rclist2 = new ArrayList<>();
            rclist2.add(rolecode);
            rclist2.add((String) _rcs);
            map.put(requestMapping, rclist2);
        }
    }
}
