package org.tio.p000mg.web.server.auth;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.mg.service.model.main.User;
import org.tio.mg.service.service.base.UserRoleService;
import org.tio.mg.service.service.base.UserService;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/auth/AccessCtrlService.class */
public class AccessCtrlService {
    private static Logger log = LoggerFactory.getLogger(AccessCtrlService.class);
    public static final String PATH_CURR_USER = "/user/curr";

    public static boolean canAccess(AccessCtrlConfig accessCtrlConfig, Integer userid, String servletPath1) throws IOException {
        Object neededRolecodes;
        if (accessCtrlConfig == null) {
            log.info("没有配置权限控制，说明不需要进行权限控制");
            return true;
        }
        if ((!Const.USE_ANONYMOUS || !PATH_CURR_USER.equals(servletPath1)) && (neededRolecodes = accessCtrlConfig.getNeededRolecodes(servletPath1)) != null) {
            boolean b = false;
            if (neededRolecodes instanceof List) {
                List<String> _neededRolecodes = (List) neededRolecodes;
                for (String neededRolecode : _neededRolecodes) {
                    b = isMeetRoleExp(neededRolecode, userid).booleanValue();
                    if (b) {
                        break;
                    }
                }
            } else {
                b = isMeetRoleExp((String) neededRolecodes, userid).booleanValue();
            }
            return b;
        }
        return true;
    }

    public static boolean hasRole(Integer userid, Byte code) throws Exception {
        User user = UserService.ME.getById(userid);
        return hasRole(user, code);
    }

    public static boolean hasRole(User user, Byte code) throws Exception {
        List<Byte> roles;
        if (user == null || !UserRoleService.checkUserStatus(user) || (roles = user.getRoles()) == null || roles.isEmpty()) {
            return false;
        }
        return roles.contains(code);
    }

    /* JADX WARN: Code restructure failed: missing block: B:81:0x0160, code lost:
    
        if (hasRole(r5, java.lang.Byte.valueOf(r4)) == false) goto L82;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private static java.lang.Boolean isMeetRoleExp(java.lang.String r4, java.lang.Integer r5) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 387
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: org.tio.p000mg.web.server.auth.AccessCtrlService.isMeetRoleExp(java.lang.String, java.lang.Integer):java.lang.Boolean");
    }

    private static String getBracketValue(String input) {
        Pattern pattern = Pattern.compile("\\({1}[^\\(\\)]+\\){1}", 2);
        Matcher matcher = pattern.matcher(input);
        String reserved = "";
        if (matcher.find()) {
            reserved = matcher.group(0);
        }
        String ret = replaceAll(reserved, "\\(|\\)", "");
        return ret;
    }

    public static void main(String[] args) {
        String ret = replaceBracketValue("4|(5&(3|6))", "false");
        System.out.println(ret);
        String xx = getBracketValue("4|(5&(3|6))");
        System.out.println(xx);
    }

    public static String replaceAll(String input, String regex, String replacement) {
        Pattern p = Pattern.compile(regex, 2);
        Matcher m = p.matcher(input);
        String result = m.replaceAll(replacement);
        return result;
    }

    private static String replaceBracketValue(String input, String value) {
        return replaceAll(input, "\\({1}[^\\(\\)]+\\){1}", value);
    }
}
