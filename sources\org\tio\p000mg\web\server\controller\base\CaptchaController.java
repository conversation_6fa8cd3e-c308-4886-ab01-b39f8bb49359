package org.tio.p000mg.web.server.controller.base;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.core.util.StrUtil;
import java.io.ByteArrayOutputStream;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;
import org.tio.mg.service.cache.CacheConfig;
import org.tio.mg.service.cache.Caches;
import org.tio.mg.service.vo.RequestExt;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.cache.ICache;
import org.tio.utils.resp.Resp;

@RequestPath("/captcha")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/base/CaptchaController.class */
public class CaptchaController {
    private static Logger log = LoggerFactory.getLogger(CaptchaController.class);

    public static Resp check(HttpRequest request, String captcha, boolean remove) {
        HttpSession session = request.getHttpSession();
        RequestExt ext = WebUtils.getRequestExt(request);
        boolean isNeed = ext.isFromBrowserPc();
        if (!isNeed) {
            return Resp.ok();
        }
        ICache cache = Caches.getCache(CacheConfig.CAPTCHA);
        String code = (String) Caches.getCache(CacheConfig.CAPTCHA).get(session.getId());
        if (remove) {
            cache.remove(session.getId());
        }
        if (StrUtil.isNotBlank(captcha) && Objects.equals(captcha.toLowerCase(), code)) {
            return Resp.ok();
        }
        if (StrUtil.isNotBlank(code)) {
            return Resp.fail().msg("校验码失效");
        }
        return Resp.fail().msg("输入的校验码不正确");
    }

    @RequestPath("")
    public HttpResponse index(HttpRequest request) throws Exception {
        HttpResponse ret = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            try {
                CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(116, 36, 4, 3);
                byteArrayOutputStream = new ByteArrayOutputStream();
                captcha.write(byteArrayOutputStream);
                byte[] bs = byteArrayOutputStream.toByteArray();
                String code = captcha.getCode();
                Caches.getCache(CacheConfig.CAPTCHA).put(request.getHttpSession().getId(), code.toLowerCase());
                ret = Resps.bytes(request, bs, "png");
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            } catch (Throwable e) {
                log.error(e.toString(), e);
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            }
            return ret;
        } catch (Throwable th) {
            if (byteArrayOutputStream != null) {
                byteArrayOutputStream.close();
            }
            throw th;
        }
    }

    @RequestPath("/validate")
    public HttpResponse validate(String captcha, HttpRequest request) throws Exception {
        Resp resp = check(request, captcha, false);
        HttpResponse response = Resps.json(request, resp);
        return response;
    }
}
