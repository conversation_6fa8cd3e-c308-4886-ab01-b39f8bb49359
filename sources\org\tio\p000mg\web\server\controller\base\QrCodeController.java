package org.tio.p000mg.web.server.controller.base;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;

@RequestPath("/qrcode")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/base/QrCodeController.class */
public class QrCodeController {
    private static Logger log = LoggerFactory.getLogger(QrCodeController.class);

    @RequestPath("/{width}/{height}")
    public HttpResponse index(Integer width, Integer height, Integer color, Integer bgColor, String str, HttpRequest request) throws Exception {
        QrConfig qrConfig = new QrConfig();
        qrConfig.setHeight(height.intValue());
        qrConfig.setWidth(width.intValue());
        qrConfig.setBackColor(bgColor.intValue());
        qrConfig.setForeColor(color.intValue());
        qrConfig.setMargin(5);
        byte[] bs = QrCodeUtil.generatePng(str, qrConfig);
        HttpResponse ret = Resps.bytes(request, bs, "png");
        return ret;
    }
}
