package org.tio.p000mg.web.server.controller.p001mg;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.server.annotation.RequestPath;
import org.tio.mg.service.model.conf.Area;
import org.tio.mg.service.service.conf.AreaService;
import org.tio.utils.resp.Resp;

@RequestPath("/area")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/AreaController.class */
public class AreaController {
    private static Logger log = LoggerFactory.getLogger(AreaController.class);

    @RequestPath("/all")
    public Resp all() throws Exception {
        List<Area> allArea = AreaService.getAreaTree();
        return Resp.ok().data(allArea);
    }

    @RequestPath("/child")
    public Resp child(String code) throws Exception {
        Area area = AreaService.getChild(code);
        return Resp.ok().data(area);
    }

    @RequestPath("/parent")
    public Resp parent(String code) throws Exception {
        Area area = AreaService.getParent(code);
        return Resp.ok().data(area);
    }
}
