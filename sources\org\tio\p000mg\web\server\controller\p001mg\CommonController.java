package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.core.io.FileUtil;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.UploadFile;
import org.tio.http.server.annotation.RequestPath;
import org.tio.mg.service.model.main.File;
import org.tio.p000mg.web.server.utils.UploadUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.utils.resp.Resp;

@RequestPath("/common")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/CommonController.class */
public class CommonController {
    private static Logger log = LoggerFactory.getLogger(CommonController.class);

    @RequestPath("/file")
    public Resp file(HttpRequest request, UploadFile uploadFile, Byte type) throws Exception {
        try {
            if (uploadFile == null) {
                return Resp.fail("上传信息为空");
            }
            File dbFile = innerUploadFile(uploadFile, type);
            return Resp.ok(dbFile);
        } catch (Exception e) {
            log.error(e.toString(), e);
            return Resp.fail("系统错误");
        }
    }

    @RequestPath("/files")
    public Resp files(HttpRequest request, UploadFile[] uploadFile, Byte type) throws Exception {
        if (uploadFile != null) {
            try {
                if (uploadFile.length > 0) {
                    List<File> dbFiles = new ArrayList<>();
                    for (UploadFile file : uploadFile) {
                        File dbFile = innerUploadFile(file, type);
                        dbFiles.add(dbFile);
                    }
                    return Resp.ok(dbFiles);
                }
            } catch (Exception e) {
                log.error(e.toString(), e);
                return Resp.fail("系统错误");
            }
        }
        return Resp.fail("上传信息为空");
    }

    private File innerUploadFile(UploadFile uploadFile, Byte type) throws Exception {
        byte[] bs = uploadFile.getData();
        String filename = uploadFile.getName();
        String ext = FileUtil.extName(filename);
        String urlWithoutExt = UploadUtils.newFile(UploadUtils.mgSubDir(type), -type.byteValue(), filename);
        String url = urlWithoutExt + "." + ext;
        java.io.File file = new java.io.File(Const.RES_ROOT, url);
        FileUtil.writeBytes(bs, file);
        File dbFile = new File();
        dbFile.setExt(ext);
        dbFile.setFilename(uploadFile.getName());
        dbFile.setSize(Long.valueOf(bs.length));
        dbFile.setUid(Integer.valueOf(-type.byteValue()));
        dbFile.setUrl(url);
        dbFile.save();
        return dbFile;
    }
}
