package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import java.util.Date;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.http.server.util.Resps;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.main.UserAgent;
import org.tio.mg.service.model.mg.MgIpInfo;
import org.tio.mg.service.model.mg.MgOperLog;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.model.mg.MgUserLoginLog;
import org.tio.mg.service.model.mg.MgUserToken;
import org.tio.mg.service.service.base.IpInfoService;
import org.tio.mg.service.service.mg.MgUserService;
import org.tio.mg.service.service.mg.MgUserTokenService;
import org.tio.mg.service.utils.PeriodUtils;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.MgLoginVo;
import org.tio.mg.service.vo.RequestExt;
import org.tio.mg.service.vo.SessionExt;
import org.tio.p000mg.web.server.init.WebApiInit;
import org.tio.p000mg.web.server.p003vo.LoginResult;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.SystemTimer;
import org.tio.utils.resp.Resp;

@RequestPath("/mgLogin")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgLoginController.class */
public class MgLoginController {
    private static Logger log = LoggerFactory.getLogger(MgLoginController.class);
    private MgUserService userService = MgUserService.ME;
    private IpInfoService ipInfoService = IpInfoService.ME;
    private MgUserTokenService mgUserTokenService = MgUserTokenService.me;

    @RequestPath("")
    public HttpResponse login(HttpRequest request) {
        MgLoginVo mgLoginVo = (MgLoginVo) JSONObject.parseObject(request.getBodyString(), MgLoginVo.class);
        if (mgLoginVo == null || StrUtil.isEmpty(mgLoginVo.getLoginName()) || StrUtil.isEmpty(mgLoginVo.getPd5())) {
            return Resps.json(request, Resp.fail("无效参数"));
        }
        String sessionId = request.getHttpSession().getId();
        HttpSession httpSession = request.getHttpSession();
        HttpConfig httpConfig = request.getHttpConfig();
        Ret ret = doLogin(mgLoginVo, request);
        if (ret.isOk()) {
            MgUser user = (MgUser) ret.get("user");
            RequestExt requestExt = WebUtils.getRequestExt(request);
            byte deviceType = requestExt.getDeviceType();
            boolean fromApp = requestExt.isFromApp();
            String ip = request.getClientIp();
            MgIpInfo ipinfo = this.ipInfoService.mgSave(ip);
            MgOperLog operLog = new MgOperLog();
            operLog.setModename("登录");
            operLog.setAid(-1);
            operLog.setOperip(ip);
            operLog.setOperparam(mgLoginVo.getLoginName() + "****" + mgLoginVo.getAuthCode());
            operLog.setOpertype(MgConst.OperLogType.SYS);
            if (fromApp) {
                operLog.setDeviceinfo(StringUtils.substring(requestExt.getDeviceinfo(), 0, 128));
            } else {
                operLog.setDeviceinfo(StringUtils.substring(request.getUserAgent(), 0, 128));
            }
            operLog.save();
            MgUserLoginLog userLoginLog = new MgUserLoginLog();
            Date time = new Date();
            userLoginLog.setIp(ip);
            userLoginLog.setIpid(ipinfo.getId());
            userLoginLog.setSessionid(sessionId);
            userLoginLog.setMguid(user.getId());
            userLoginLog.setDevicetype(Byte.valueOf(deviceType));
            userLoginLog.setDayperiod(PeriodUtils.dateToPeriodByType(time, (byte) 1));
            userLoginLog.setTimeperiod(PeriodUtils.dateToPeriodByType(time, (byte) 7));
            userLoginLog.setHourperiod(PeriodUtils.dateToPeriodByType(time, (byte) 6));
            userLoginLog.setTime(time);
            if (fromApp) {
                userLoginLog.setDeviceinfo(StringUtils.substring(requestExt.getDeviceinfo(), 0, 128));
                userLoginLog.setImei(requestExt.getImei());
            } else {
                UserAgent userAgent = requestExt.getUserAgent();
                if (userAgent != null) {
                    userLoginLog.setDeviceinfo(userAgent.getOsName() + " " + userAgent.getOsVersion() + "/" + userAgent.getAgentName() + " " + userAgent.getAgentVersionMajor());
                } else {
                    userLoginLog.setDeviceinfo(StringUtils.substring(request.getUserAgent(), 0, 128));
                }
            }
            userLoginLog.save();
            HttpResponse httpResponse = Resps.json(request, Resp.ok());
            WebApiInit.requestHandler.updateSessionId(request, httpSession, httpResponse);
            String newSeesionId = request.getHttpSession().getId();
            MgUserToken userToken = this.mgUserTokenService.find(deviceType, user.getId().intValue());
            if (userToken == null) {
                MgUserToken userToken2 = new MgUserToken();
                userToken2.setMguid(user.getId());
                userToken2.setDevicetype(Byte.valueOf(deviceType));
                userToken2.setToken(newSeesionId);
                this.mgUserTokenService.add(userToken2);
            } else {
                String oldToken = userToken.getToken();
                HttpSession oldHttpSession = httpConfig.getSessionStore().get(oldToken);
                if (oldHttpSession != null) {
                    SessionExt oldSessionExt = oldHttpSession.getAttribute("SESSION_EXT", SessionExt.class, new SessionExt(), httpConfig);
                    oldSessionExt.setUid((Integer) null);
                    oldHttpSession.update(httpConfig);
                }
                userToken.setToken(newSeesionId);
                this.mgUserTokenService.update(userToken);
            }
            SessionExt sessionExt = (SessionExt) httpSession.getAttribute("SESSION_EXT", SessionExt.class);
            sessionExt.setUid(user.getId());
            sessionExt.setLoginTime(Long.valueOf(SystemTimer.currTime));
            httpSession.update(httpConfig);
            return httpResponse;
        }
        HttpResponse httpResponse2 = (HttpResponse) ret.get("resp");
        if (httpResponse2 == null) {
            log.error("doLogin(loginname, pwd, authcode, request)返回值没有包含response信息");
            return Resps.json(request, Resp.fail("服务器异常"));
        }
        return httpResponse2;
    }

    private Ret doLogin(MgLoginVo mgLoginVo, HttpRequest request) {
        Ret ret = this.userService.login(mgLoginVo);
        if (ret.isFail()) {
            Resp resp = Resp.fail();
            Integer code = RetUtils.getIntCode(ret);
            if (code == null || code.intValue() == 1 || code.intValue() == 2) {
                resp.code(1);
                resp.msg(LoginResult.ErrorCode.USER_OR_PWD_ERROR.value);
            } else {
                resp.msg(LoginResult.ErrorCode.AUTH_CODE_ERROR.value);
                resp.code(3);
            }
            HttpResponse httpResponse = Resps.json(request, resp);
            return Ret.fail().set("resp", httpResponse);
        }
        MgUser user = (MgUser) RetUtils.getOkTData(ret);
        if (user != null) {
            Resp resp2 = checkStatus(user);
            if (resp2.isOk()) {
                return Ret.ok().set("user", user);
            }
            HttpResponse httpResponse2 = Resps.json(request, resp2);
            return Ret.fail().set("resp", httpResponse2);
        }
        Resp resp3 = Resp.fail();
        resp3.code(LoginResult.ErrorCode.USER_OR_PWD_ERROR.code).msg(LoginResult.ErrorCode.USER_OR_PWD_ERROR.value);
        HttpResponse httpResponse3 = Resps.json(request, resp3);
        return Ret.fail().set("resp", httpResponse3);
    }

    private static Resp checkStatus(MgUser user) {
        if (Objects.equals(user.getStatus(), (byte) 1)) {
            return Resp.ok();
        }
        if (Objects.equals(user.getStatus(), (byte) 5)) {
            return Resp.fail().code(LoginResult.ErrorCode.USER_INBLACK_ERROR.code).msg(LoginResult.ErrorCode.USER_INBLACK_ERROR.value);
        }
        return Resp.fail().code(LoginResult.ErrorCode.USER_STATUS_ERROR.code).msg(LoginResult.ErrorCode.USER_STATUS_ERROR.value);
    }
}
