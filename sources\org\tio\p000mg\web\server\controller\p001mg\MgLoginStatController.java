package org.tio.p000mg.web.server.controller.p001mg;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.mg.MgLoginStatService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RequestVo;
import org.tio.utils.resp.Resp;

@RequestPath("/mgloginstat")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgLoginStatController.class */
public class MgLoginStatController {
    private Logger log = LoggerFactory.getLogger(MgLoginStatController.class);
    private MgLoginStatService loginStatService = MgLoginStatService.ME;

    @RequestPath("/timeList")
    public Resp timeList(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.statTimeList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取登录-时间-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/timeUserList")
    public Resp timeUserList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.statTimeUserList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取登录-时间-用户-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/userList")
    public Resp userList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.statUserList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取登录-用户-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/loginList")
    public Resp loginList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.loginList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取日志列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/loginInfoList")
    public Resp loginInfoList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.statLoginList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取登录-时间-用户-日志-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/userDayList")
    public Resp userDayList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginStatService.statUserDayList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取登录-IP-天-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }
}
