package org.tio.p000mg.web.server.controller.p001mg;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.annotation.RequestPath;
import org.tio.mg.service.model.mg.MgOperLog;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.model.mg.MgUserToken;
import org.tio.mg.service.service.mg.MgUserTokenService;
import org.tio.mg.service.vo.RequestExt;
import org.tio.mg.service.vo.SessionExt;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/mgLoginOut")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgLogoutController.class */
public class MgLogoutController {
    private static Logger log = LoggerFactory.getLogger(MgLogoutController.class);

    @RequestPath("")
    public Resp logout(HttpRequest request) throws Exception {
        MgUserToken userToken;
        SessionExt sessionExtInOtherSession;
        HttpConfig httpConfig = request.getHttpConfig();
        HttpSession httpSession = request.getHttpSession();
        String sessionId = httpSession.getId();
        MgUser user = WebUtils.currUser(request);
        if (user != null) {
            SessionExt sessionExt = (SessionExt) httpSession.getAttribute("SESSION_EXT", SessionExt.class);
            sessionExt.setUid((Integer) null);
            sessionExt.setLoginTime((Long) null);
            sessionExt.setKickedInfo((MgOperLog) null);
            httpSession.update(httpConfig);
            RequestExt requestExt = WebUtils.getRequestExt(request);
            int deviceType = requestExt.getDeviceType();
            int c = MgUserTokenService.me.delete(deviceType, user.getId().intValue(), sessionId);
            if (c <= 0 && (userToken = MgUserTokenService.me.find(deviceType, user.getId().intValue())) != null) {
                String tokenInDb = userToken.getToken();
                HttpSession otherHttpSession = httpConfig.getHttpSession(tokenInDb);
                if (otherHttpSession != null) {
                    Integer useridInOtherSession = WebUtils.getUserIdBySession(otherHttpSession);
                    if (useridInOtherSession != null && (sessionExtInOtherSession = (SessionExt) otherHttpSession.getAttribute("SESSION_EXT", SessionExt.class)) != null) {
                        sessionExtInOtherSession.setUid((Integer) null);
                        otherHttpSession.update(httpConfig);
                    }
                }
            }
            Resp resp = Resp.ok();
            return resp;
        }
        Resp resp2 = Resp.fail("你并未登录");
        return resp2;
    }
}
