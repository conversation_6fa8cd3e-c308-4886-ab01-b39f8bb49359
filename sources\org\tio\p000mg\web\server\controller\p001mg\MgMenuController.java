package org.tio.p000mg.web.server.controller.p001mg;

import com.alibaba.fastjson.JSONObject;
import java.sql.SQLException;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.mg.service.model.mg.MgMenu;
import org.tio.mg.service.model.mg.MgRole;
import org.tio.mg.service.model.mg.MgRoleMenu;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.model.mg.MgUserRole;
import org.tio.mg.service.service.atom.AbsAtom;
import org.tio.mg.service.service.mg.MgMenuService;
import org.tio.mg.service.service.mg.MgRoleMenuService;
import org.tio.mg.service.service.mg.MgRoleService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.MgMenuRoleVo;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/mgmenu")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgMenuController.class */
public class MgMenuController {
    private static Logger log = LoggerFactory.getLogger(MgMenuController.class);
    private MgMenuService menuService = MgMenuService.ME;
    private MgRoleService roleService = MgRoleService.ME;
    private MgRoleMenuService roleMenuService = MgRoleMenuService.ME;

    @RequestPath("/add")
    public Resp addMenu(@NotNull final HttpRequest request) {
        final MgMenu mgMenu = (MgMenu) JSONObject.parseObject(request.getBodyString(), MgMenu.class);
        if (mgMenu == null || StrUtil.isEmpty(mgMenu.getPath()) || StrUtil.isEmpty(mgMenu.getName()) || mgMenu.getType().byteValue() < 0 || StrUtil.isEmpty(mgMenu.getTitle())) {
            return Resp.fail("无效参数");
        }
        Ret res = this.menuService.add(mgMenu);
        if (res.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(res));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgMenuController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台菜单新增", (byte) 1, "后台新增菜单名称为" + mgMenu.getName());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(res));
    }

    @RequestPath("/update")
    public Resp updateMenu(final HttpRequest request) {
        final MgMenu mgMenu = (MgMenu) JSONObject.parseObject(request.getBodyString(), MgMenu.class);
        if (mgMenu == null || mgMenu.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.menuService.update(mgMenu);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgMenuController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台菜单修改", (byte) 2, "后台修改菜单id为" + mgMenu.getId());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/del")
    public Resp delMenu(final HttpRequest request, final Integer id) {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.menuService.del(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgMenuController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台菜单删除", MgConst.OperLogTypes.del, "后台删除菜单id为" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/queryMenuList")
    public Resp queryMenuList(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.menuService.getMenuList(requestVo);
        if (ret.isFail()) {
            log.error("获取菜单失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/queryMenuTree")
    public Resp queryMenuTree(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.menuService.queryMenuTree(requestVo);
        if (ret.isFail()) {
            log.error("获取菜单失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/queryRoleMenuList")
    public Resp queryRoleMenuList(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getRoleId() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.roleMenuService.queryRoleMenuList(requestVo.getRoleId());
        if (ret.isFail()) {
            log.error("获取菜单失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/getRoleMenuList")
    public Resp getRoleMenuList(HttpRequest request) {
        MgUser user = WebUtils.currUser(request);
        if (user == null) {
            return Resp.fail("获取菜单失败:用户不存在");
        }
        MgUserRole mgUserRole = this.roleService.getRoleByUid(user.getId());
        if (mgUserRole == null) {
            return Resp.fail("获取菜单失败:用户角色未找到");
        }
        Ret ret = this.roleMenuService.queryRoleMenuList(mgUserRole.getId());
        if (ret.isFail()) {
            log.error("获取菜单失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/saveMenuRoleList")
    public Resp saveMenuRoleList(HttpRequest request) throws Exception {
        MgMenuRoleVo mgMenuRoleVo = (MgMenuRoleVo) JSONObject.parseObject(request.getBodyString(), MgMenuRoleVo.class);
        if (mgMenuRoleVo == null || mgMenuRoleVo.getRoleId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        final Integer roleId = mgMenuRoleVo.getRoleId();
        final List<Integer> menuIds = mgMenuRoleVo.getMenuIds();
        MgRole mgRole = this.roleService.getRoleByRoleId(roleId);
        if (mgRole == null) {
            return Resp.fail("保存菜单失败:用户角色未找到");
        }
        AbsAtom absAtom = new AbsAtom() { // from class: org.tio.mg.web.server.controller.mg.MgMenuController.4
            public boolean run() throws SQLException {
                Db.use("tio_mg").update("delete from mg_role_menu where role_id = ?", new Object[]{roleId});
                for (int i = 0; i < menuIds.size(); i++) {
                    Integer menuId = (Integer) menuIds.get(i);
                    MgRoleMenu mgRoleMenu = new MgRoleMenu();
                    mgRoleMenu.setRoleId(roleId);
                    mgRoleMenu.setMenuId(menuId);
                    Ret res = MgMenuController.this.roleMenuService.add(mgRoleMenu);
                    if (res.isFail()) {
                        setMsg("添加记录失败");
                        return false;
                    }
                }
                return true;
            }
        };
        boolean tx = Db.use("tio_mg").tx(absAtom);
        if (tx) {
            return Resp.ok("操作成功");
        }
        return Resp.fail(absAtom.getMsg());
    }
}
