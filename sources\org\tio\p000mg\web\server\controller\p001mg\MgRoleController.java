package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.mg.MgRole;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.service.mg.MgRoleService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/adminRole")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgRoleController.class */
public class MgRoleController {
    private Logger log = LoggerFactory.getLogger(MgRoleController.class);
    private MgRoleService roleService = MgRoleService.ME;

    @RequestPath("/add")
    public Resp add(final HttpRequest request) throws Exception {
        final MgRole mgRole = (MgRole) JSONObject.parseObject(request.getBodyString(), MgRole.class);
        if (mgRole == null || StrUtil.isEmpty(mgRole.getName())) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.roleService.add(mgRole);
        if (ret.isFail()) {
            this.log.error("保存失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgRoleController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "角色新增", (byte) 1, "角色新增" + mgRole.getName());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/del")
    public Resp del(final HttpRequest request, final Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.roleService.del(id);
        if (ret.isFail()) {
            this.log.error("删除失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgRoleController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "角色删除", MgConst.OperLogTypes.del, "角色删除 rid为" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) throws Exception {
        final MgRole mgRole = (MgRole) JSONObject.parseObject(request.getBodyString(), MgRole.class);
        if (mgRole == null || mgRole.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.roleService.update(mgRole);
        if (ret.isFail()) {
            this.log.error("修改失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgRoleController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "角色修改", (byte) 2, "角色修改" + mgRole.getName());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/list")
    public Resp list(HttpRequest request, Byte status) throws Exception {
        Ret ret = this.roleService.list(status);
        if (ret.isFail()) {
            this.log.error("获取角色失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/getRoutes")
    public Resp getRoutes(HttpRequest request) throws Exception {
        MgUser mgUser = WebUtils.currUser(request);
        if (mgUser == null || mgUser.getId() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.roleService.getRoutes(mgUser.getId());
        if (ret.isFail()) {
            this.log.error("获取菜单失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }
}
