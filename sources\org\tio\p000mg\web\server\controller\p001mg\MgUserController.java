package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.service.mg.MgUserService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/adminUser")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/MgUserController.class */
public class MgUserController {
    private MgUserService userService = MgUserService.ME;

    @RequestPath("/getUserInfo")
    public Resp getUserInfo(HttpRequest request) throws Exception {
        MgUser user = WebUtils.currUser(request);
        if (user != null) {
            return Resp.ok(user);
        }
        return Resp.fail();
    }

    @RequestPath("/add")
    public Resp add(final HttpRequest request) throws Exception {
        final MgUser mgUser = (MgUser) JSONObject.parseObject(request.getBodyString(), MgUser.class);
        if (mgUser == null || StrUtil.isEmpty(mgUser.getLoginname()) || StrUtil.isEmpty(mgUser.getPwd())) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.add(mgUser);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgUserController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户新增", (byte) 1, "后台新增用户为" + mgUser.getLoginname());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/del")
    public Resp del(final HttpRequest request, final Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.del(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgUserController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户删除", MgConst.OperLogTypes.del, "后台用户删除id为" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/updatePwd")
    public Resp updatePwd(final HttpRequest request) throws Exception {
        final RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (null == requestVo.getId() || requestVo.getId().intValue() < 0 || StrUtil.isEmpty(requestVo.getOldPwd()) || StrUtil.isEmpty(requestVo.getNewPwd())) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.updatePwd(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgUserController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户修改密码", (byte) 2, "后台用户修改id为" + requestVo.getId());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) throws Exception {
        final MgUser mgUser = (MgUser) JSONObject.parseObject(request.getBodyString(), MgUser.class);
        if (mgUser == null || mgUser.getId() == null || mgUser.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.update(mgUser);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgUserController.4
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户修改", (byte) 2, "后台用户修改id为" + mgUser.getId());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/resetPwd")
    public Resp resetPwd(final HttpRequest request, final Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.resetPwd(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.MgUserController.5
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "重置后台登录密码", MgConst.OperLogTypes.rest, "重置后台登录密码id为" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/list")
    public Resp list(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.userService.userList(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }
}
