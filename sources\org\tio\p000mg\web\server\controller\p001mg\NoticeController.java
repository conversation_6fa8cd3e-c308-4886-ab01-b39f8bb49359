package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import java.util.HashMap;
import java.util.Map;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.conf.Announcement;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.service.conf.NoticeService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.HttpRequestUtils;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/notice")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/NoticeController.class */
public class NoticeController {
    private NoticeService noticeService = NoticeService.me;

    @RequestPath("/list")
    public Resp getList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.noticeService.list(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/add")
    public Resp addNotice(final HttpRequest request) throws Exception {
        Announcement announcement = (Announcement) JSONObject.parseObject(request.getBodyString(), Announcement.class);
        if (announcement == null || StrUtil.isEmpty(announcement.getContent())) {
            return Resp.fail("无效参数");
        }
        MgUser user = WebUtils.currUser(request);
        if (user == null) {
            return Resp.fail("操作失败");
        }
        announcement.setAuthor(user.getLoginname());
        announcement.setOperatorId(user.getId());
        Ret ret = this.noticeService.add(announcement);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.NoticeController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户新增", (byte) 1, "后台用户新增通知公告");
            }
        }).start();
        return Resp.ok("操作成功");
    }

    @RequestPath("/update")
    public Resp updateNotice(final HttpRequest request) throws Exception {
        final Announcement announcement = (Announcement) JSONObject.parseObject(request.getBodyString(), Announcement.class);
        if (announcement == null || announcement.getId() == null) {
            return Resp.fail("无效参数");
        }
        MgUser user = WebUtils.currUser(request);
        if (user == null) {
            return Resp.fail("操作失败");
        }
        announcement.setOperatorId(user.getId());
        Ret ret = this.noticeService.update(announcement);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.NoticeController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户修改", (byte) 2, "后台用户修改通知公告id：" + announcement.getId());
            }
        }).start();
        return Resp.ok("操作成功");
    }

    @RequestPath("/del")
    public Resp delNotice(final HttpRequest request, final Integer id) throws Exception {
        if (id == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.noticeService.del(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.NoticeController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台用户删除", MgConst.OperLogTypes.del, "后台用户删除通知公告id：" + id);
            }
        }).start();
        return Resp.ok("操作成功");
    }

    @RequestPath("/publish")
    public Resp publishNotice(HttpRequest request, Integer id) throws Exception {
        if (id == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.noticeService.updateStatus(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("noticeId", id);
        String url = "http://127.0.0.1:6060/mytio/chat/pushAllNotice.tio_x?" + HttpRequestUtils.getInstance().getUrlParams(paramsMap);
        String resultJson = HttpUtil.post(url, paramsMap);
        System.out.println("返回结果：" + resultJson);
        if (StrUtil.isEmpty(resultJson)) {
            return Resp.fail("操作失败");
        }
        Resp resp = (Resp) JSONObject.parseObject(resultJson, Resp.class);
        if (resp.isOk()) {
            return Resp.ok("操作成功");
        }
        return resp;
    }

    @RequestPath("/stop")
    public Resp stopNotice(HttpRequest request, Integer id) throws Exception {
        if (id == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.noticeService.stop(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok("操作成功");
    }
}
