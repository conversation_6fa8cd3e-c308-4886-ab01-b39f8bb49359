package org.tio.p000mg.web.server.controller.p001mg;

import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.mg.service.service.conf.MgConfService;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.utils.resp.Resp;

@RequestPath("/sys")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/SysController.class */
public class SysController {
    private static Logger log = LoggerFactory.getLogger(SysController.class);

    @RequestPath("/params")
    public Resp operdisable(HttpRequest request) {
        Map<String, Object> param = new HashMap<>();
        param.put("res.server", Const.RES_SERVER);
        param.put("site", Const.SITE);
        param.put("google_auth", MgConfService.getString("isOpen.admin.googleCheck", "2"));
        param.put("bsResUrl", MgConfService.getString("bsResUrl", Const.RES_SERVER));
        String sitename = MgConfService.getString("sitename", "鸽哒");
        param.put("sitename", sitename);
        param.put("tioim_title", MgConfService.getString("tioim.title", "鸽哒IM"));
        param.put("admin_title", sitename + "后台管理系统");
        param.put("resetpwd", "888888");
        return Resp.ok(param);
    }
}
