package org.tio.p000mg.web.server.controller.p001mg;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.utils.jfinal.P;

@RequestPath("/upload")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/UploadController.class */
public class UploadController {
    private static Logger log = LoggerFactory.getLogger(UploadController.class);

    @RequestPath("/img")
    public List<Record> img(HttpRequest request) throws Exception {
        return m0xx(request, "select * from img where updatetime > ?");
    }

    @RequestPath("/video")
    public List<Record> video(HttpRequest request) throws Exception {
        return m0xx(request, "select * from video where updatetime > ?");
    }

    @RequestPath("/all")
    public List<Map<String, Object>> all(HttpRequest request) throws Exception {
        final String rootPath = P.get("res.root");
        List<File> list = FileUtil.loopFiles(rootPath, new FileFilter() { // from class: org.tio.mg.web.server.controller.mg.UploadController.1
            @Override // java.io.FileFilter
            public boolean accept(File pathname) {
                try {
                    String path = pathname.getCanonicalPath().substring(rootPath.length());
                    if (StrUtil.replace(path, "\\", "/").startsWith("/avatar/")) {
                        return false;
                    }
                    return true;
                } catch (IOException e) {
                    UploadController.log.error(e.toString(), e);
                    return true;
                }
            }
        });
        List<Map<String, Object>> listMap = new ArrayList<>(list.size());
        for (File file : list) {
            try {
                Map<String, Object> map = new HashMap<>();
                listMap.add(map);
                String path = file.getCanonicalPath().substring(rootPath.length());
                String path2 = StrUtil.replace(path, "\\", "/");
                map.put("size", Long.valueOf(FileUtil.size(file)));
                map.put("path", path2);
            } catch (Exception e) {
                log.error(e.toString(), e);
            }
        }
        return listMap;
    }

    /* renamed from: xx */
    public List<Record> m0xx(HttpRequest request, String sql) throws Exception {
        Date date = new Date(System.currentTimeMillis() - 2592000000L);
        List<Record> list = Db.use("tio_site_main").find(sql, new Object[]{date});
        return list;
    }
}
