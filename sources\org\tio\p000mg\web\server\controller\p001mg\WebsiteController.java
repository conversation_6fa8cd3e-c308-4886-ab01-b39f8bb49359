package org.tio.p000mg.web.server.controller.p001mg;

import com.alibaba.fastjson.JSONObject;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.init.RedisInit;
import org.tio.mg.service.model.conf.Website;
import org.tio.mg.service.service.conf.MgWebsiteService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.sitexxx.servicecommon.vo.topic.TopicVo;
import org.tio.utils.resp.Resp;

@RequestPath("/website")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/mg/WebsiteController.class */
public class WebsiteController {
    private Logger log = LoggerFactory.getLogger(WebsiteController.class);
    private MgWebsiteService mgWebsiteService = MgWebsiteService.me;

    @RequestPath("/add")
    public Resp add(final HttpRequest request) {
        final Website website = (Website) JSONObject.parseObject(request.getBodyString(), Website.class);
        if (website == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.mgWebsiteService.add(website);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 3);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.WebsiteController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "自定义网站新增", (byte) 1, "新增自定义网站" + website.getSiteurl());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) {
        final Website website = (Website) JSONObject.parseObject(request.getBodyString(), Website.class);
        if (website == null || website.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.mgWebsiteService.update(website);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 3);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.WebsiteController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "自定义网站修改", (byte) 2, "修改自定义网站" + website.getSiteurl());
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/list")
    public Resp list(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.mgWebsiteService.queryList(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/del")
    public Resp del(final HttpRequest request, final Integer id) {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret del = this.mgWebsiteService.del(id);
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 3);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.mg.WebsiteController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "自定义网站删除", MgConst.OperLogTypes.del, "删除自定义网站id:" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getRetMsg(del));
    }
}
