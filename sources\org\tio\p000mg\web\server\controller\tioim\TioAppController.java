package org.tio.p000mg.web.server.controller.tioim;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.main.WxApp;
import org.tio.mg.service.service.tioim.TioAppService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/wxapp")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioAppController.class */
public class TioAppController {
    private static Logger log = LoggerFactory.getLogger(TioAppController.class);
    private TioAppService appService = TioAppService.me;

    @RequestPath("/list")
    public Resp list(HttpRequest request, String version, Byte status, Byte type, Byte mode, Integer pageNumber, Integer pageSize) throws Exception {
        Ret ret = this.appService.appList(pageNumber, pageSize, version, mode, type, status);
        if (ret.isFail()) {
            log.error("获取版本列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/add")
    public Resp add(HttpRequest request, WxApp app) throws Exception {
        Ret ret = this.appService.add(app);
        if (ret.isFail()) {
            log.error("新增失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/update")
    public Resp update(HttpRequest request, WxApp app) throws Exception {
        Ret ret = this.appService.update(app);
        if (ret.isFail()) {
            log.error("修改失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/del")
    public Resp del(HttpRequest request, Integer id) throws Exception {
        Ret ret = this.appService.del(id);
        if (ret.isFail()) {
            log.error("修改失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }
}
