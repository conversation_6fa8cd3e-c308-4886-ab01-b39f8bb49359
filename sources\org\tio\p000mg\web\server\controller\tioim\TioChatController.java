package org.tio.p000mg.web.server.controller.tioim;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.tioim.TioChatService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RequestVo;
import org.tio.utils.resp.Resp;

@RequestPath("/wxchat")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioChatController.class */
public class TioChatController {
    private static Logger log = LoggerFactory.getLogger(TioChatController.class);
    private TioChatService chatService = TioChatService.me;

    @RequestPath("/chatList")
    public Resp chatList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.chatItemList(requestVo);
        if (ret.isFail()) {
            log.error("获取会话列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/chatMsgList")
    public Resp chatMsgList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getChatLinkId() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.chatMsgList(requestVo);
        if (ret.isFail()) {
            log.error("获取会话消息失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/groupList")
    public Resp groupList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.groupList(requestVo);
        if (ret.isFail()) {
            log.error("获取群列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/friendList")
    public Resp friendList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.friendList(requestVo);
        if (ret.isFail()) {
            log.error("获取好友列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/friendApplyList")
    public Resp friendApplyList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.applyList(requestVo);
        if (ret.isFail()) {
            log.error("获取好友列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/friendMsgList")
    public Resp friendMsgList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getToUid() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.friendMsgList(requestVo);
        if (ret.isFail()) {
            log.error("获取好友消息列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/groupMsgList")
    public Resp groupMsgList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getGroupId() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.groupMsgList(requestVo);
        if (ret.isFail()) {
            log.error("获取群消息列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/groupInfo")
    public Resp groupInfo(HttpRequest request, Long groupId) throws Exception {
        if (groupId == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.groupInfo(groupId);
        if (ret.isFail()) {
            log.error("获取群信息失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/groupUserList")
    public Resp groupUserList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getGroupId() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.chatService.groupUserList(requestVo);
        if (ret.isFail()) {
            log.error("获取群用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }
}
