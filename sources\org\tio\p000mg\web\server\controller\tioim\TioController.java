package org.tio.p000mg.web.server.controller.tioim;

import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.mg.service.init.RedisInit;
import org.tio.sitexxx.servicecommon.vo.topic.CleanViewCacheVo;
import org.tio.sitexxx.servicecommon.vo.topic.TopicVo;
import org.tio.utils.resp.Resp;

@RequestPath("/tio")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioController.class */
public class TioController {
    @RequestPath("/clearuser")
    public Resp chatlist(HttpRequest request) throws Exception {
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 5);
        topic.publish(topicVo);
        return Resp.ok("操作成功");
    }

    @RequestPath("/clearconf")
    public Resp clearconf(HttpRequest request) throws Exception {
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 6);
        topic.publish(topicVo);
        return Resp.ok("操作成功");
    }

    @RequestPath("/clearstaticres")
    public Resp clearstaticres(HttpRequest request) throws Exception {
        CleanViewCacheVo cleanViewCacheVo = new CleanViewCacheVo();
        RTopic topic = RedisInit.get().getTopic("CLEAN_VIEW_CACHE");
        topic.publish(cleanViewCacheVo);
        return Resp.ok("操作成功");
    }
}
