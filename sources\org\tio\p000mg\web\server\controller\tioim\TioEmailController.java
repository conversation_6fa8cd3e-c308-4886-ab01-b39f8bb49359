package org.tio.p000mg.web.server.controller.tioim;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.conf.EmailServer;
import org.tio.mg.service.service.tioim.TioEmailService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/email")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioEmailController.class */
public class TioEmailController {
    private static Logger log = LoggerFactory.getLogger(TioEmailController.class);
    private TioEmailService emailService = TioEmailService.me;

    public static void main(String[] args) {
    }

    @RequestPath("/submit")
    public Resp submit(HttpRequest request, Integer startid, Integer endid, String content, String title) throws Exception {
        Ret ret = this.emailService.sendEmail(startid, endid, title, content);
        if (ret.isFail()) {
            log.error("发送失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/list")
    public Resp list(HttpRequest request, String searchkey) throws Exception {
        List<EmailServer> emailServers = this.emailService.getAll(searchkey);
        return Resp.ok(emailServers);
    }

    @RequestPath("/add")
    public Resp add(HttpRequest request, EmailServer email) throws Exception {
        Ret ret = this.emailService.add(email);
        if (ret.isFail()) {
            log.error("新增失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/update")
    public Resp update(HttpRequest request, EmailServer email) throws Exception {
        Ret ret = this.emailService.update(email);
        if (ret.isFail()) {
            log.error("修改失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/del")
    public Resp del(HttpRequest request, String email) throws Exception {
        Ret ret = this.emailService.del(email);
        if (ret.isFail()) {
            log.error("修改失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }
}
