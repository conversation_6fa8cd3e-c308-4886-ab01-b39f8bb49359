package org.tio.p000mg.web.server.controller.tioim;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.tioim.TioFriendService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/friend")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioFriendController.class */
public class TioFriendController {
    private Logger log = LoggerFactory.getLogger(TioFriendController.class);
    private TioFriendService friendService = TioFriendService.me;

    @RequestPath("/recordList")
    public Resp recordList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.friendService.recordList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取好友列表(消息模型)失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/clearAllMsg")
    public Resp clearAllMsg(final HttpRequest request) throws Exception {
        final Ret ret = this.friendService.cleanAllMsg();
        if (ret.isFail()) {
            this.log.error("清除好友列表(消息模型)失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioFriendController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "聊天记录清除", MgConst.OperLogTypes.del, "聊天记录清除，id为" + RetUtils.getOkList(ret));
            }
        }).start();
        return Resp.ok("清除成功");
    }

    @RequestPath("/clearMsg")
    public Resp clearMsg(final HttpRequest request, final Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.friendService.cleanMsg(id);
        if (ret.isFail()) {
            this.log.error("清除好友消息列表(消息模型)失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioFriendController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "好友聊天记录清除", MgConst.OperLogTypes.del, "清除的会话id" + id);
            }
        }).start();
        return Resp.ok("清除成功");
    }
}
