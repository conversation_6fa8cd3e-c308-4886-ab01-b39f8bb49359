package org.tio.p000mg.web.server.controller.tioim;

import com.alibaba.fastjson.JSONObject;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.tioim.TioGroupService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/group")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioGroupController.class */
public class TioGroupController {
    private Logger log = LoggerFactory.getLogger(TioGroupController.class);
    private TioGroupService groupService = TioGroupService.me;

    @RequestPath("/list")
    public Resp list(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.groupService.groupList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取群列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/dellist")
    public Resp delList(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.groupService.delgroupList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取群列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/alllist")
    public Resp allList(HttpRequest request) {
        Ret ret;
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (Objects.equals(requestVo.getType(), (byte) 1)) {
            ret = this.groupService.groupList(requestVo);
        } else {
            ret = this.groupService.delgroupList(requestVo);
        }
        if (ret.isFail()) {
            this.log.error("获取群列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/recordList")
    public Resp recordList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.groupService.recordList(requestVo);
        if (ret.isFail()) {
            this.log.error("获取群列表(消息模型)失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/clearAllMsg")
    public Resp clearAllMsg(final HttpRequest request) throws Exception {
        final Ret ret = this.groupService.cleanAllMsg();
        if (ret.isFail()) {
            this.log.error("清除群列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioGroupController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "群聊天记录清除", MgConst.OperLogTypes.del, "群聊天记录清除，id为" + RetUtils.getOkList(ret));
            }
        }).start();
        return Resp.ok("清除成功");
    }

    @RequestPath("/clearMsg")
    public Resp clearMsg(final HttpRequest request, Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        final Ret ret = this.groupService.cleanMsg(id);
        if (ret.isFail()) {
            this.log.error("清除群消息列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioGroupController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "群聊天记录清除", MgConst.OperLogTypes.del, "群聊天记录清除，id为" + RetUtils.getOkList(ret));
            }
        }).start();
        return Resp.ok("清除成功");
    }
}
