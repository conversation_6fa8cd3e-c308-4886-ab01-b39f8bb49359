package org.tio.p000mg.web.server.controller.tioim;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.main.UserInviteAttr;
import org.tio.mg.service.service.base.UserService;
import org.tio.mg.service.service.tioim.TioInviteUserService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RequestVo;
import org.tio.utils.resp.Resp;

@RequestPath("/tioinviteuser")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioInviteUserController.class */
public class TioInviteUserController {
    private static Logger log = LoggerFactory.getLogger(TioInviteUserController.class);
    private TioInviteUserService inviteUserService = TioInviteUserService.me;

    @RequestPath("/list")
    public Resp list(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.inviteUserService.list(requestVo);
        if (ret.isFail()) {
            log.error("获取用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/add")
    public Resp add(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (null == requestVo.getUid() || requestVo.getUid().intValue() < 0 || null == requestVo.getInviteCode()) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.inviteUserService.add(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(requestVo.getUid());
        return Resp.ok(ret);
    }

    @RequestPath("/update")
    public Resp update(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (null == requestVo.getUid() || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.inviteUserService.updateInfo(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(requestVo.getUid());
        return Resp.ok(ret);
    }

    @RequestPath("/unbind")
    public Resp unbind(HttpRequest request, Integer id) throws Exception {
        if (null == id || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        UserInviteAttr userInviteAttr = UserInviteAttr.dao.findById(id);
        if (userInviteAttr == null) {
            return Resp.fail("推荐用户信息未找到");
        }
        Ret ret = this.inviteUserService.unbind(userInviteAttr);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(userInviteAttr.getUid());
        return Resp.ok(ret);
    }
}
