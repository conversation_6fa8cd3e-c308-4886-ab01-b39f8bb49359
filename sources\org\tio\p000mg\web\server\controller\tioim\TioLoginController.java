package org.tio.p000mg.web.server.controller.tioim;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.tioim.TioLoginService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RequestVo;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/tiologin")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioLoginController.class */
public class TioLoginController {
    private static Logger log = LoggerFactory.getLogger(TioLoginController.class);
    private TioLoginService loginService = TioLoginService.me;

    @RequestPath("/loginLogList")
    public Resp loginLogList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginService.loginList(requestVo);
        if (ret.isFail()) {
            log.error("获取日志列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/timeList")
    public Resp timeList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginService.statTimeList(requestVo);
        if (ret.isFail()) {
            log.error("获取登录-时间-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/timeUserList")
    public Resp timeUserList(HttpRequest request, String period, Integer pageNumber, Integer pageSize) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginService.statTimeUserList(requestVo);
        if (ret.isFail()) {
            log.error("获取登录-时间-用户-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/timeloginlist")
    public Resp timeLoginList(HttpRequest request, String period, Integer uid) throws Exception {
        Ret ret = this.loginService.statTimeLoginList(period, uid);
        if (ret.isFail()) {
            log.error("获取登录-时间-用户-日志-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/ipList")
    public Resp ipList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginService.statIpList(requestVo);
        if (ret.isFail()) {
            log.error("获取登录-ip-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/ipDayList")
    public Resp ipDayList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || StrUtil.isEmpty(requestVo.getIp())) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.loginService.statIpDayList(requestVo);
        if (ret.isFail()) {
            log.error("获取登录-IP-天-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/ipUserList")
    public Resp ipUserList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || StrUtil.isEmpty(requestVo.getPeriod()) || StrUtil.isEmpty(requestVo.getIp())) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.loginService.statIpUserList(requestVo);
        if (ret.isFail()) {
            log.error("获取登录-ip-用户-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/iploginlist")
    public Resp iploginlist(HttpRequest request, String period, Integer uid, String ip) throws Exception {
        Ret ret = this.loginService.statIpLoginList(period, uid, ip);
        if (ret.isFail()) {
            log.error("获取登录-ip-用户-日志-统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }
}
