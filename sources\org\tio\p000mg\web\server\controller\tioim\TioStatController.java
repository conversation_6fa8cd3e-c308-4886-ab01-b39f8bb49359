package org.tio.p000mg.web.server.controller.tioim;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Kv;
import org.tio.jfinal.kit.Ret;
import org.tio.jfinal.plugin.activerecord.Db;
import org.tio.jfinal.plugin.activerecord.Page;
import org.tio.jfinal.plugin.activerecord.Record;
import org.tio.jfinal.plugin.activerecord.SqlPara;
import org.tio.mg.service.service.tioim.TioGroupService;
import org.tio.mg.service.service.tioim.TioLoginService;
import org.tio.mg.service.service.tioim.TioStatService;
import org.tio.mg.service.service.tioim.TioUserService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RequestVo;
import org.tio.utils.resp.Resp;

@RequestPath("/stat")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioStatController.class */
public class TioStatController {
    private static Logger log = LoggerFactory.getLogger(TioStatController.class);
    private TioStatService statService = TioStatService.me;
    private TioUserService userService = TioUserService.me;
    private TioGroupService groupService = TioGroupService.me;
    private TioLoginService loginService = TioLoginService.me;

    @RequestPath("/userCount")
    public Resp userCount(HttpRequest request) {
        Ret ret = this.userService.getUserCount((RequestVo) null);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/userRegisterCount")
    public Resp userRegisterCount(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.userService.getUserCount(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/userLoginCount")
    public Resp userLoginCount(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.loginService.getLoginCount(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/userGroupCount")
    public Resp userGroupCount(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.groupService.getGroupCount(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/userRegisterStat")
    public Resp userRegisterStat(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getType() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.userRegisterStat(requestVo);
        if (ret.isFail()) {
            log.error("获取用户统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/statUser")
    public Resp userLoginRegiestCount(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || StrUtil.isEmpty(requestVo.getStartTime()) || StrUtil.isEmpty(requestVo.getEndTime()) || requestVo.getStatType() == null || requestVo.getStatPeriod() == null) {
            return Resp.fail("无效参数");
        }
        Kv params = Kv.by("startTime", requestVo.getStartTime()).set("endTime", requestVo.getEndTime()).set("statType", requestVo.getStatType()).set("statPeriod", requestVo.getStatPeriod());
        SqlPara sqlPara = Db.use("tio_site_stat").getSqlPara("stat.statUser", params);
        Page<Record> records = Db.use("tio_site_stat").paginate(1, 30, sqlPara);
        return Resp.ok(records.getList());
    }

    @RequestPath("/userstatlist")
    public Resp userstatlist(HttpRequest request, String searchkey, Integer pageNumber, Integer pageSize) throws Exception {
        Ret ret = this.statService.userStatList(pageNumber, pageSize, searchkey);
        if (ret.isFail()) {
            log.error("获取用户统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/groupList")
    public Resp groupList(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getType() == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.groupStat(requestVo);
        if (ret.isFail()) {
            log.error("获取用户统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/userIpTimeRegisterStat")
    public Resp userIpTimeRegisterStat(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.statService.userIpTimeRegisterStat(requestVo);
        if (ret.isFail()) {
            log.error("获取用户ip下的时间统计列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/loginCountById")
    public Resp loginCountById(HttpRequest request, Integer uid) throws Exception {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.userLoginCount(uid);
        if (ret.isFail()) {
            log.error("获取总登录次数：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/ipRelateCount")
    public Resp ipRelateCount(HttpRequest request, String ip) throws Exception {
        if (StrUtil.isEmpty(ip)) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.ipRegisterCount(ip);
        if (ret.isFail()) {
            log.error("获取ip注册次数：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/areaRelateCount")
    public Resp areaRelateCount(HttpRequest request, String province, String city) throws Exception {
        if (StrUtil.isEmpty(province) || StrUtil.isEmpty(city)) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.areaRegisterCount(province, city);
        if (ret.isFail()) {
            log.error("获取区域注册次数：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/timeRelateCount")
    public Resp timeRelateCount(HttpRequest request, String period) throws Exception {
        if (StrUtil.isEmpty(period)) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.statService.timeRegisterCount(period);
        if (ret.isFail()) {
            log.error("获取时间注册次数：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/areaList")
    public Resp areaList(HttpRequest request) throws Exception {
        Ret ret = this.statService.areadict();
        if (ret.isFail()) {
            log.error("获取区域字典失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }
}
