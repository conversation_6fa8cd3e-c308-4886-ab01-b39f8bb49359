package org.tio.p000mg.web.server.controller.tioim;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.conf.YxConf;
import org.tio.mg.service.model.main.User;
import org.tio.mg.service.service.base.UserService;
import org.tio.mg.service.service.tioim.TioInviteService;
import org.tio.mg.service.service.tioim.TioUserService;
import org.tio.mg.service.service.yxnxun.conf.AdminConfService;
import org.tio.mg.service.service.yxnxun.other.ImportUsersService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.HttpRequestUtils;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WxGroupAvatarUtil;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/tiouser")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/controller/tioim/TioUserController.class */
public class TioUserController {
    private static Logger log = LoggerFactory.getLogger(TioUserController.class);
    private TioUserService userService = TioUserService.me;
    private TioInviteService inviteService = TioInviteService.me;

    @RequestPath("/list")
    public Resp list(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.userService.list(requestVo);
        if (ret.isFail()) {
            log.error("获取用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/info")
    public Resp info(HttpRequest request, Integer uid) throws Exception {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.info(uid);
        if (ret.isFail()) {
            log.error("获取用户信息失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/statList")
    public Resp statList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.userService.statList(requestVo);
        if (ret.isFail()) {
            log.error("获取用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/resetPwd")
    public Resp resetPwd(final HttpRequest request, final Integer uid) throws Exception {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.resetPwd(uid);
        if (ret.isFail()) {
            log.error("重置密码失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(uid);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioUserController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "用户密码重置", MgConst.OperLogTypes.rest, "用户密码重置，id为" + uid);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/disable")
    public Resp disable(final HttpRequest request, final Integer uid, Byte status) throws Exception {
        Ret ret = this.userService.disable(uid, status);
        if (ret.isFail()) {
            log.error("禁用/启用失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(uid);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioUserController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "用户禁用", MgConst.OperLogTypes.disable, "群聊天记录清除，id为" + uid);
            }
        }).start();
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/delMsg")
    public Resp delMsgInfo(final HttpRequest request, final Integer uid) {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.delMsgByUid(uid);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioUserController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "删除聊天记录（单方）", MgConst.OperLogTypes.del, "删除聊天记录，id为" + uid);
            }
        }).start();
        return Resp.ok("删除成功");
    }

    @RequestPath("/resetPayPwd")
    public Resp resetPayPwd(final HttpRequest request, final Integer uid) throws Exception {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.resetPayPwd(uid);
        if (ret.isFail()) {
            log.error("重置密码失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(uid);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioUserController.4
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "重置支付密码", MgConst.OperLogTypes.rest, "重置支付密码，id为" + uid);
            }
        }).start();
        return Resp.ok(ret);
    }

    @RequestPath("/updateVipLevel")
    public Resp updateVipLevel(HttpRequest request, Integer uid, Integer vipLevel) throws Exception {
        if (uid == null || uid.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.updateVipLevel(uid, vipLevel);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(uid);
        return Resp.ok(ret);
    }

    @RequestPath("/addImUser")
    public Resp addImUser(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || StrUtil.isEmpty(requestVo.getNick()) || StrUtil.isEmpty(requestVo.getPd5())) {
            return Resp.fail("无效参数");
        }
        YxConf validateType = AdminConfService.ME.findByName("validateType");
        Map<String, Object> paramsMap = new HashMap<>();
        if ("2".equals(validateType.getValue())) {
            if (StrUtil.isEmpty(requestVo.getEmail())) {
                return Resp.fail("无效参数");
            }
            paramsMap.put("email", requestVo.getEmail());
            paramsMap.put("loginname", requestVo.getEmail());
        } else {
            if (StrUtil.isEmpty(requestVo.getLoginName())) {
                return Resp.fail("无效参数");
            }
            if (!StrUtil.isEmpty(requestVo.getLoginName())) {
                paramsMap.put("loginname", requestVo.getLoginName());
            }
        }
        if (requestVo.getUid() != null) {
            if (requestVo.getUid().intValue() < 10000000 || requestVo.getUid().intValue() > 99999999) {
                return Resp.fail("uid必须为8位数字，且0不能开头");
            }
            paramsMap.put("id", requestVo.getUid());
        }
        if (!StrUtil.isEmpty(requestVo.getPd5())) {
            paramsMap.put("pwd", requestVo.getPd5());
        }
        if (!StrUtil.isEmpty(requestVo.getNick())) {
            paramsMap.put("nick", requestVo.getNick());
        }
        if (!StrUtil.isEmpty(requestVo.getAvatar())) {
            paramsMap.put("avatar", requestVo.getAvatar());
        }
        paramsMap.put("code", "ABCDEF");
        paramsMap.put("agreement", "on");
        String url = "http://127.0.0.1:6060/mytio/register/1.tio_x?" + HttpRequestUtils.getInstance().getUrlParams(paramsMap);
        String resultJson = HttpUtil.post(url, paramsMap);
        if (StrUtil.isEmpty(resultJson)) {
            return Resp.fail();
        }
        Resp resp = (Resp) JSONObject.parseObject(resultJson, Resp.class);
        if (resp.isOk()) {
            return Resp.ok("操作成功");
        }
        return resp;
    }

    @RequestPath("/addUser")
    public Resp addUser(final HttpRequest request) {
        final RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || StrUtil.isEmpty(requestVo.getLoginName()) || StrUtil.isEmpty(requestVo.getNick()) || StrUtil.isEmpty(requestVo.getPd5())) {
            return Resp.fail("无效参数");
        }
        if (requestVo.getUid() != null) {
            if (requestVo.getUid().intValue() < 10000000 || requestVo.getUid().intValue() > 99999999) {
                return Resp.fail("UID必须为8位纯数字");
            }
            User first = User.dao.findById(requestVo.getUid());
            if (first != null) {
                return Resp.fail("UID已存在");
            }
        }
        String avatarPath = null;
        if (StrUtil.isBlank(requestVo.getAvatar())) {
            try {
                avatarPath = WxGroupAvatarUtil.pressUserAvatar(requestVo.getNick());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            avatarPath = requestVo.getAvatar();
        }
        requestVo.setAvatar(avatarPath);
        requestVo.setIp(request.getClientIp());
        Ret ret = ImportUsersService.ME.addUser(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.controller.tioim.TioUserController.5
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "用户新增", (byte) 1, "新增用户，账号为：" + requestVo.getLoginName());
            }
        }).start();
        return Resp.ok();
    }

    @RequestPath("/delUser")
    public Resp delUser(HttpRequest request, Integer id) {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.userService.delUser(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        UserService.ME.notifyClearCache(id);
        return Resp.ok(RetUtils.getRetMsg(ret));
    }

    @RequestPath("/getInviteListByUid")
    public Resp getInviteListByUid(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getUid() == null || requestVo.getUid().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.inviteService.getPageList(requestVo);
        if (ret.isOk()) {
            return Resp.ok(RetUtils.getOkPage(ret));
        }
        return Resp.fail("获取失败");
    }
}
