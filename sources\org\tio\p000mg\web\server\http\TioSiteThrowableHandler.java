package org.tio.p000mg.web.server.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.RequestLine;
import org.tio.http.server.intf.ThrowableHandler;
import org.tio.http.server.util.Resps;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.vo.RequestExt;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/TioSiteThrowableHandler.class */
public class TioSiteThrowableHandler implements ThrowableHandler {
    private static Logger log = LoggerFactory.getLogger(TioSiteThrowableHandler.class);

    /* renamed from: ME */
    public static final TioSiteThrowableHandler f0ME = new TioSiteThrowableHandler();

    public HttpResponse handler(HttpRequest request, RequestLine requestLine, Throwable throwable) {
        MgUser curr = WebUtils.currUser(request);
        if (curr != null) {
            log.error(curr.toString() + "\r\n" + request.toString(), throwable);
        } else {
            log.error(request.getHttpSession().getId() + "\r\n" + request.toString(), throwable);
        }
        HttpResponse ret = Resps.json(request, Resp.fail());
        RequestExt requestExt = WebUtils.getRequestExt(request);
        requestExt.setCanCache(false);
        return ret;
    }
}
