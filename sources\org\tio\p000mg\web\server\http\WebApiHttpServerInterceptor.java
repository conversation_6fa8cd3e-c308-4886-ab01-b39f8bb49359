package org.tio.p000mg.web.server.http;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.Cookie;
import org.tio.http.common.HeaderName;
import org.tio.http.common.HeaderValue;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.HttpResponseStatus;
import org.tio.http.common.Method;
import org.tio.http.common.MimeType;
import org.tio.http.common.RequestLine;
import org.tio.http.common.session.HttpSession;
import org.tio.http.common.utils.HttpGzipUtils;
import org.tio.http.server.intf.HttpServerInterceptor;
import org.tio.http.server.util.Resps;
import org.tio.mg.service.cache.CacheConfig;
import org.tio.mg.service.cache.Caches;
import org.tio.mg.service.model.conf.Httpcache;
import org.tio.mg.service.model.main.IpInfo;
import org.tio.mg.service.model.main.UserAgent;
import org.tio.mg.service.model.mg.MgOperLog;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.model.stat.TioSlowRequest;
import org.tio.mg.service.service.base.IpInfoService;
import org.tio.mg.service.service.base.UserAgentService;
import org.tio.mg.service.service.conf.HttpcacheService;
import org.tio.mg.service.service.conf.IpWhiteListService;
import org.tio.mg.service.service.conf.MgConfService;
import org.tio.mg.service.vo.RequestExt;
import org.tio.mg.service.vo.SessionExt;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.utils.SystemTimer;
import org.tio.utils.cache.ICache;
import org.tio.utils.jfinal.P;
import org.tio.utils.json.Json;
import org.tio.utils.lock.LockUtils;
import org.tio.utils.resp.Resp;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/WebApiHttpServerInterceptor.class */
public class WebApiHttpServerInterceptor implements HttpServerInterceptor {
    private final String httpCacheLockKey = getClass().getName() + ".httpCacheLockKey";
    private static boolean userHttpCache;
    private static Set<String> pathWhiteSet;
    private static Logger log = LoggerFactory.getLogger(WebApiHttpServerInterceptor.class);

    /* renamed from: ME */
    public static final WebApiHttpServerInterceptor f1ME = new WebApiHttpServerInterceptor();
    private static final HeaderName HTTPCACHE_FLAG_HEADER_NAME = HeaderName.from("tio-httpcache-old");
    private static final HeaderName HTTPCACHE_FIRST_HEADER_NAME = HeaderName.from("tio-httpcache-new");
    private static final HeaderName HEADER_NAME_WEBAPI_SERVER = HeaderName.from("tio-webapi-server");
    private static final HeaderValue HEADER_VALUE_WHICH_API = HeaderValue.from(Const.MY_IP_API);
    private static final Object lockForGetLock = new Object();
    private static byte[] BODY_BYTES_NEED_ACCESS_TOKEN = null;

    static {
        userHttpCache = P.getInt("web.api.use.http.cache", 1).intValue() == 1;
        pathWhiteSet = new HashSet();
        pathWhiteSet.add("/mgLogin");
        pathWhiteSet.add("/sys/params");
        pathWhiteSet.add("/common/file");
        pathWhiteSet.add("/conf");
        pathWhiteSet.add("/conf/getByName");
        pathWhiteSet.add("/stat");
    }

    private static boolean isWhitePath(String path) {
        if (pathWhiteSet.contains(path)) {
            return true;
        }
        return false;
    }

    public HttpResponse doBeforeHandler(HttpRequest request, RequestLine requestLine, HttpResponse httpResponseFromCache) throws Exception {
        RequestExt requestExt = new RequestExt();
        request.setAttribute("TIO_SITE_REQUESTEXT", requestExt);
        requestExt.setCanCache(false);
        String path = requestLine.getPath();
        Method method = requestLine.getMethod();
        String params = "";
        if (method == Method.GET) {
            params = Json.toJson(request.getParams());
        } else if (method == Method.POST) {
            params = request.getBodyString();
        }
        log.warn("请求URL：{};\n请求方式：{};\n请求IP：{};\n请求参数：\n{}", new Object[]{requestLine.getPathAndQuery(), method.toString(), request.getClientIp(), params});
        IpInfo ipInfo = IpInfoService.ME.save(request.getClientIp());
        requestExt.setIpInfo(ipInfo);
        String userAgentStr = request.getUserAgent();
        UserAgent userAgent = UserAgentService.ME.save(userAgentStr);
        requestExt.setUserAgent(userAgent);
        requestExt.setFromBrowser(true);
        requestExt.setFromBrowserPc(true);
        boolean isWhitePath = isWhitePath(path);
        if (!isWhitePath) {
            boolean isWhiteIp = IpWhiteListService.isWhiteIp(request.getClientIp());
            int accessTokenOn = MgConfService.getInt("use.access.token.pc", 2).intValue();
            if (!isWhiteIp && accessTokenOn == 1) {
                Cookie cookie = request.getCookie("tio_mg_access_token");
                boolean needNewAccessToken = true;
                if (cookie != null && StrUtil.isNotBlank(cookie.getValue())) {
                    ICache cache2 = Caches.getCache(CacheConfig.MG_TIO_ACCESS_TOKEN);
                    String valueInCache = (String) cache2.get(request.getHttpSession().getId(), String.class);
                    if (Objects.equals(cookie.getValue(), valueInCache)) {
                        needNewAccessToken = false;
                    }
                }
                if (needNewAccessToken) {
                    return Resps.bytesWithContentType(request, BODY_BYTES_NEED_ACCESS_TOKEN, MimeType.TEXT_PLAIN_JSON.getType());
                }
            }
            MgUser user = WebUtils.currUser(request);
            if (user == null) {
                HttpSession session = request.getHttpSession();
                SessionExt sessionExt = WebUtils.getSessionExt(session);
                MgOperLog loginLog = sessionExt.getKickedInfo();
                if (loginLog != null) {
                    String ip = loginLog.getOperip();
                    Date time = loginLog.getUpdatetime();
                    String deviceinfo = loginLog.getDeviceinfo();
                    String msg = "异地登录，您的帐号于" + DateUtil.formatDateTime(time) + "在" + ip + "登录过";
                    if (StrUtil.isNotBlank(deviceinfo)) {
                        msg = msg + "，登录设备【" + deviceinfo + "】";
                    }
                    Resp resp = Resp.fail(msg).code(1003);
                    return Resps.json(request, resp);
                }
                Resp resp2 = Resp.fail("您尚未登录或登录超时").code(1001);
                return Resps.json(request, resp2);
            }
        }
        requestExt.setCanCache(true);
        return doHttpCacheOnBeforeHandler(request, requestExt, path, this.httpCacheLockKey, userHttpCache);
    }

    public static HttpResponse doHttpCacheOnBeforeHandler(HttpRequest request, RequestExt requestExt, String path, String httpCacheLockKey, boolean userHttpCache2) throws Exception {
        Httpcache httpcache;
        ICache cache = null;
        if (userHttpCache2) {
            cache = HttpcacheService.getCache(path);
        }
        if (userHttpCache2 && cache != null && (httpcache = HttpcacheService.get(path)) != null) {
            String cacheKey = getHttpCacheKey(request, cache, httpcache);
            HttpResponse httpResponse = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
            if (httpResponse != null) {
                return cloneAnd304(request, requestExt, httpResponse);
            }
            ReentrantReadWriteLock lock = LockUtils.getReentrantReadWriteLock(cacheKey, lockForGetLock);
            ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
            boolean tryWrite = writeLock.tryLock();
            if (tryWrite) {
                request.setAttribute(httpCacheLockKey, writeLock);
                HttpResponse httpResponse2 = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
                if (httpResponse2 != null) {
                    return cloneAnd304(request, requestExt, httpResponse2);
                }
                return null;
            }
            ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
            boolean tryRead = readLock.tryLock(10L, TimeUnit.SECONDS);
            if (tryRead) {
                request.setAttribute(httpCacheLockKey, readLock);
                HttpResponse httpResponse3 = (HttpResponse) cache.get(cacheKey, HttpResponse.class);
                if (httpResponse3 != null) {
                    return cloneAnd304(request, requestExt, httpResponse3);
                }
                return null;
            }
            return null;
        }
        return null;
    }

    private static HttpResponse cloneAnd304(HttpRequest request, RequestExt requestExt, HttpResponse httpResponse) throws NumberFormatException {
        HttpResponse clone = HttpResponse.cloneResponse(request, httpResponse);
        requestExt.setFromCache(true);
        HeaderValue lastModified = clone.getLastModified();
        if (lastModified != null) {
            try {
                long _lastModified = Long.parseLong(lastModified.value);
                HttpResponse r304 = Resps.try304(request, _lastModified);
                if (r304 != null) {
                    r304.addHeader(HTTPCACHE_FLAG_HEADER_NAME, clone.getHeader(HTTPCACHE_FLAG_HEADER_NAME));
                    return r304;
                }
            } catch (NumberFormatException e) {
                return clone;
            }
        }
        return clone;
    }

    public void doAfterHandler(HttpRequest request, RequestLine requestLine, HttpResponse response, long cost) throws Exception {
        RequestExt requestExt = WebUtils.getRequestExt(request);
        response.addHeader(HeaderName.Access_Control_Allow_Credentials, HeaderValue.TRUE);
        doHttpCacheOnAfterHandler(response, request, requestExt, requestLine.path, userHttpCache, this.httpCacheLockKey);
        saveSlowRequest(request, requestLine, response, cost, (byte) 1);
    }

    public static HttpResponse doHttpCacheOnAfterHandler(HttpResponse response, HttpRequest request, RequestExt requestExt, String path, boolean userHttpCache2, String httpCacheLockKey) {
        Httpcache httpcache;
        String cacheKey = null;
        try {
            try {
            } catch (Exception e) {
                log.error(e.toString(), e);
                try {
                    Lock lock = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock.getClass().getName(), path, cacheKey});
                        lock.unlock();
                    }
                } catch (Exception e2) {
                    log.error(request.requestLine.toString(), e2);
                }
            }
            if (requestExt.isFromCache()) {
                try {
                    Lock lock2 = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock2 != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock2.getClass().getName(), path, null});
                        lock2.unlock();
                    }
                } catch (Exception e3) {
                    log.error(request.requestLine.toString(), e3);
                }
                return response;
            }
            ICache cache = null;
            if (userHttpCache2) {
                cache = HttpcacheService.getCache(path);
            }
            if (!userHttpCache2 || cache == null || (httpcache = HttpcacheService.get(path)) == null || response == null || response.getStatus() != HttpResponseStatus.C200 || !requestExt.isCanCache()) {
                try {
                    Lock lock3 = (Lock) request.getAttribute(httpCacheLockKey);
                    if (lock3 != null) {
                        if (path == null) {
                            path = request.requestLine.getPath();
                        }
                        log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock3.getClass().getName(), path, null});
                        lock3.unlock();
                    }
                } catch (Exception e4) {
                    log.error(request.requestLine.toString(), e4);
                }
                return response;
            }
            cacheKey = getHttpCacheKey(request, cache, httpcache);
            HeaderValue headerValueCacheKey = HeaderValue.from(cacheKey);
            HeaderValue lastModified = HeaderValue.from(SystemTimer.currTime + "");
            response.setLastModified(lastModified);
            HttpGzipUtils.gzip(request, response);
            HttpResponse responseForCache = HttpResponse.cloneResponse(request, response);
            responseForCache.addHeader(HTTPCACHE_FLAG_HEADER_NAME, headerValueCacheKey);
            cache.put(cacheKey, responseForCache);
            response.addHeader(HTTPCACHE_FIRST_HEADER_NAME, headerValueCacheKey);
            response.addHeader(HEADER_NAME_WEBAPI_SERVER, HEADER_VALUE_WHICH_API);
            try {
                Lock lock4 = (Lock) request.getAttribute(httpCacheLockKey);
                if (lock4 != null) {
                    if (path == null) {
                        path = request.requestLine.getPath();
                    }
                    log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock4.getClass().getName(), path, cacheKey});
                    lock4.unlock();
                }
            } catch (Exception e5) {
                log.error(request.requestLine.toString(), e5);
            }
            return response;
        } catch (Throwable th) {
            try {
                Lock lock5 = (Lock) request.getAttribute(httpCacheLockKey);
                if (lock5 != null) {
                    if (path == null) {
                        path = request.requestLine.getPath();
                    }
                    log.info("httpcache释放锁【{}】, 这是正常日志. path:【{}】, cacheKey:{}", new Object[]{lock5.getClass().getName(), path, cacheKey});
                    lock5.unlock();
                }
            } catch (Exception e6) {
                log.error(request.requestLine.toString(), e6);
            }
            throw th;
        }
    }

    public static void saveSlowRequest(HttpRequest request, RequestLine requestLine, HttpResponse response, long cost, byte type) {
        int slow_request_cost = MgConfService.getInt("slow_request_cost", 2000).intValue();
        if (cost >= slow_request_cost) {
            try {
                Date endtime = new Date();
                Date starttime = new Date(endtime.getTime() - cost);
                Integer uid = WebUtils.currUserId(request);
                String path = requestLine.getPathAndQuery();
                TioSlowRequest tioSlowRequest = new TioSlowRequest();
                tioSlowRequest.setType(Byte.valueOf(type));
                tioSlowRequest.setCost(Long.valueOf(cost));
                tioSlowRequest.setPath(path);
                if (!Objects.equals("/register/submit", path)) {
                    tioSlowRequest.setBody(StrUtil.subPre(request.getBodyString(), 1024));
                }
                tioSlowRequest.setEndtime(endtime);
                tioSlowRequest.setStarttime(starttime);
                tioSlowRequest.setUid(uid);
                tioSlowRequest.setSession(request.getHttpSession().getId());
                tioSlowRequest.save();
            } catch (Exception e) {
                log.error(e.toString(), e);
            }
        }
    }

    private static String getHttpCacheKey(HttpRequest request, ICache cache, Httpcache httpcache) {
        Integer currUid = WebUtils.currUserId(request);
        Map<String, Object> params = null;
        String[] paramArray = httpcache.getParamArray();
        if (paramArray != null && paramArray.length > 0) {
            params = new HashMap<>();
            for (String name : paramArray) {
                String value = request.getParam(name);
                params.put(name, value);
            }
        }
        return getHttpCacheKey(currUid, params, cache, httpcache);
    }

    public static String getHttpCacheKey(Integer currUid, Map<String, Object> params, ICache cache, Httpcache httpcache) {
        String[] paramArray = httpcache.getParamArray();
        boolean isUseUidAsKey = httpcache.isUseUidAsKey();
        boolean isUseLoginedAsKey = httpcache.isUseLoginedAsKey();
        StringBuilder key = new StringBuilder(30);
        if (isUseUidAsKey && currUid != null) {
            key.append("u{").append(currUid).append("}");
        }
        if (isUseLoginedAsKey) {
            if (currUid != null) {
                key.append("l{1}");
            } else {
                key.append("l{0}");
            }
        }
        if (paramArray != null && params != null) {
            key.append("p{");
            for (String name : paramArray) {
                Object value = params.get(name);
                if (value != null) {
                    key.append(name).append("=").append(value).append("&");
                }
            }
            key.append("}");
        }
        if (key.length() == 0) {
            return "t-io";
        }
        return key.toString();
    }

    public static void removeHttpCache(String path, Map<String, Object> params, Integer currUid) {
        Httpcache httpcache;
        ICache cache = HttpcacheService.getCache(path);
        if (cache != null && (httpcache = HttpcacheService.get(path)) != null) {
            if (httpcache.isHasPageNumber()) {
                if (params == null) {
                    params = new HashMap();
                }
                for (int i = 0; i < 15; i++) {
                    params.put("pageNumber", Integer.valueOf(i));
                    String cacheKey = getHttpCacheKey(currUid, params, cache, httpcache);
                    cache.remove(cacheKey);
                }
                return;
            }
            String cacheKey2 = getHttpCacheKey(currUid, params, cache, httpcache);
            cache.remove(cacheKey2);
        }
    }

    public static void clearHttpCache(String path) {
        ICache cache = HttpcacheService.getCache(path);
        if (cache != null) {
            cache.clear();
        }
    }
}
