package org.tio.p000mg.web.server.http;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.session.HttpSession;
import org.tio.http.server.session.HttpSessionListener;
import org.tio.mg.service.vo.SessionExt;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/WebApiHttpSessionListener.class */
public class WebApiHttpSessionListener implements HttpSessionListener {
    private static Logger log = LoggerFactory.getLogger(WebApiHttpSessionListener.class);

    /* renamed from: ME */
    public static final WebApiHttpSessionListener f2ME = new WebApiHttpSessionListener();

    private WebApiHttpSessionListener() {
    }

    public void doAfterCreated(HttpRequest request, HttpSession session, HttpConfig httpConfig) {
        String newSessionId = decorateSessionId(request, session.getId());
        session.setId(newSessionId);
        session.setAttribute("SESSION_EXT", new SessionExt(), httpConfig);
    }

    private static String decorateSessionId(HttpRequest request, String initSessionId) {
        StringBuilder sb = new StringBuilder(initSessionId.length() + 5);
        sb.append(StrUtil.sub(initSessionId, 0, 2)).append("1");
        sb.append(StrUtil.sub(initSessionId, 2, 4)).append("4");
        sb.append(StrUtil.sub(initSessionId, 4, 5)).append("7");
        sb.append(StrUtil.sub(initSessionId, 5, 6)).append("2");
        sb.append(StrUtil.sub(initSessionId, 6, 8)).append("5");
        sb.append(StrUtil.sub(initSessionId, 8, 9)).append("8");
        sb.append(StrUtil.sub(initSessionId, 9, 12)).append("3");
        sb.append(StrUtil.sub(initSessionId, 9, initSessionId.length()));
        return sb.toString();
    }

    public static boolean isValidSessionId(String s) {
        if (s.length() < 13) {
            return false;
        }
        int i = 0 + 1;
        if (s.charAt(2 + 0) == '1') {
            int i2 = i + 1;
            if (s.charAt(4 + i) == '4') {
                int i3 = i2 + 1;
                if (s.charAt(5 + i2) == '7') {
                    int i4 = i3 + 1;
                    if (s.charAt(6 + i3) == '2') {
                        int i5 = i4 + 1;
                        if (s.charAt(8 + i4) == '5') {
                            int i6 = i5 + 1;
                            if (s.charAt(9 + i5) == '8') {
                                int i7 = i6 + 1;
                                if (s.charAt(12 + i6) == '3') {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }
}
