package org.tio.p000mg.web.server.http;

import cn.hutool.core.util.StrUtil;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HeaderName;
import org.tio.http.common.HeaderValue;
import org.tio.http.common.HttpRequest;
import org.tio.http.common.HttpResponse;
import org.tio.http.common.session.limiter.SessionRateLimiter;
import org.tio.http.common.session.limiter.SessionRateVo;
import org.tio.mg.service.service.conf.IpWhiteListService;
import org.tio.utils.SystemTimer;
import org.tio.utils.json.Json;
import org.tio.utils.resp.Resp;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/WebApiSessionRateLimiter.class */
public class WebApiSessionRateLimiter implements SessionRateLimiter {
    private static Logger log = LoggerFactory.getLogger(WebApiSessionRateLimiter.class);

    /* renamed from: me */
    public static final WebApiSessionRateLimiter f3me = new WebApiSessionRateLimiter();
    private final Map<String, Integer> intervalMap = new HashMap();
    private final Map<String, Integer> prefixMap = new HashMap();
    private final Map<String, Integer> allowCountMap = new HashMap();
    private HttpResponse response;

    private WebApiSessionRateLimiter() {
        this.prefixMap.put("/product/download/", 120000);
        this.allowCountMap.put("/tiomsg/groupmodelist", 9999);
        this.allowCountMap.put("/tiouser/statlist", 9999);
        this.allowCountMap.put("/tiomsg/grouplist", 9999);
        this.allowCountMap.put("/friend/fdlist", 9999);
        this.allowCountMap.put("/tiomsg/p2plist", 9999);
        this.response = new HttpResponse();
        try {
            this.response.setBody(Json.toJson(Resp.fail("小兄Dei，你的手速有点快，单身多少年了？").code(1005)).getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            log.error(e.toString(), e);
        }
        this.response.addHeader(HeaderName.Content_Type, HeaderValue.Content_Type.TEXT_PLAIN_JSON);
    }

    public boolean allow(HttpRequest request, SessionRateVo sessionRateVo) {
        if (IpWhiteListService.isWhiteIp(request.getClientIp())) {
            return true;
        }
        String path = request.getRequestLine().getPath();
        Integer allowCount = this.allowCountMap.get(path);
        if (allowCount == null) {
            allowCount = 120;
        }
        if (sessionRateVo.getAccessCount().get() > allowCount.intValue()) {
            return false;
        }
        Integer iv = this.intervalMap.get(path);
        if (iv == null && this.prefixMap.size() > 0) {
            Set<Map.Entry<String, Integer>> set = this.prefixMap.entrySet();
            Iterator<Map.Entry<String, Integer>> it = set.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Map.Entry<String, Integer> entry = it.next();
                if (StrUtil.startWith(path, entry.getKey())) {
                    iv = entry.getValue();
                    break;
                }
            }
        }
        if (iv != null && SystemTimer.currTime - sessionRateVo.getLastAccessTime() < iv.intValue()) {
            return false;
        }
        return true;
    }

    public HttpResponse response(HttpRequest request, SessionRateVo sessionRateVo) {
        return this.response;
    }
}
