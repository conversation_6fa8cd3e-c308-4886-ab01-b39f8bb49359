package org.tio.p000mg.web.server.http.stat.p002ip;

import cn.hutool.core.io.FileUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.TioConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.handler.DefaultHttpRequestHandler;
import org.tio.http.server.stat.ip.path.IpAccessStat;
import org.tio.http.server.stat.ip.path.IpPathAccessStat;
import org.tio.http.server.stat.ip.path.IpPathAccessStatListener;
import org.tio.mg.service.model.stat.TioIpPathAccessStat;
import org.tio.mg.service.service.base.IpInfoService;
import org.tio.mg.service.service.base.TioIpPathAccessStatService;
import org.tio.mg.service.service.conf.IpWhiteListService;
import org.tio.mg.service.service.conf.MgConfService;
import org.tio.p000mg.web.server.http.WebApiHttpSessionListener;
import org.tio.p000mg.web.server.utils.TioIpPullblackUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.json.Json;
import org.tio.utils.lock.MapWithLock;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/stat/ip/TioSiteIpPathAccessStatListener.class */
public class TioSiteIpPathAccessStatListener implements IpPathAccessStatListener {
    private byte appType;
    private static final String CONF_PREFIX = "ip.access.";
    private static final int STEP;
    private static final int MAX_NO_SESSION_COUNT;
    private static final int MAX_SESSION_COUNT;
    private static final int G1_MAX_COUNT;
    private static final int G1_MAX_PER_SECOND;
    private static final int G2_MAX_COUNT;
    private static final int G2_MAX_PER_SECOND;
    private static Logger log = LoggerFactory.getLogger(TioSiteIpPathAccessStatListener.class);
    public static final TioSiteIpPathAccessStatListener ME_SITE_VIEW = new TioSiteIpPathAccessStatListener((byte) 9);
    public static final TioSiteIpPathAccessStatListener ME_SITE_API = new TioSiteIpPathAccessStatListener((byte) 8);
    private static Set<String> skipExtSet = new HashSet();

    static {
        skipExtSet.add("css");
        skipExtSet.add("js");
        skipExtSet.add("ico");
        skipExtSet.add("png");
        skipExtSet.add("jpg");
        skipExtSet.add("swf");
        skipExtSet.add("xml");
        skipExtSet.add("gif");
        skipExtSet.add("jpeg");
        skipExtSet.add("woff");
        skipExtSet.add("map");
        skipExtSet.add("txt");
        skipExtSet.add("mp4");
        skipExtSet.add("m3u8");
        skipExtSet.add("svg");
        STEP = MgConfService.getInt("ip.access.STEP", 200).intValue();
        MAX_NO_SESSION_COUNT = MgConfService.getInt("ip.access.MAX_NO_SESSION_COUNT", 1000).intValue();
        MAX_SESSION_COUNT = MgConfService.getInt("ip.access.MAX_SESSION_COUNT", 1000).intValue();
        G1_MAX_COUNT = MgConfService.getInt("ip.access.G1_MAX_COUNT", 3000).intValue();
        G1_MAX_PER_SECOND = MgConfService.getInt("ip.access.G1_MAX_PER_SECOND", 5).intValue();
        G2_MAX_COUNT = MgConfService.getInt("ip.access.G2_MAX_COUNT", 1000).intValue();
        G2_MAX_PER_SECOND = MgConfService.getInt("ip.access.G2_MAX_PER_SECOND", 50).intValue();
    }

    public TioSiteIpPathAccessStatListener(byte appType) {
        this.appType = (byte) 9;
        this.appType = appType;
    }

    public TioIpPathAccessStat toDbObj(IpAccessStat ipAccessStat) {
        TioIpPathAccessStat ret = new TioIpPathAccessStat();
        int requestCount = ipAccessStat.count.get();
        long timeCost = ipAccessStat.timeCost.get();
        long duration = ipAccessStat.getDuration();
        ret.setAppType(Byte.valueOf(this.appType));
        ret.setRequestCount(Integer.valueOf(requestCount));
        ret.setTimeCost(Long.valueOf(timeCost));
        ret.setTimeCostPerRequest(Double.valueOf(timeCost / requestCount));
        ret.setDuration(Long.valueOf(duration));
        ret.setDurationType(ipAccessStat.getDurationType());
        ret.setFirstAccessTime(new Date(ipAccessStat.getFirstAccessTime()));
        ret.setFormatedDuration(ipAccessStat.getFormatedDuration());
        ret.setIp(ipAccessStat.getIp());
        ret.setIpid(IpInfoService.ME.save(ipAccessStat.getIp()).getId());
        ret.setNoSessionCount(Integer.valueOf(ipAccessStat.noSessionCount.get()));
        ret.setPath((String) null);
        ret.setRequestCountPerSecond(Double.valueOf(ipAccessStat.getPerSecond()));
        ret.setServer(Const.MY_IP_API);
        return ret;
    }

    public TioIpPathAccessStat toDbObj(IpPathAccessStat ipPathAccessStat) {
        TioIpPathAccessStat ret = new TioIpPathAccessStat();
        int requestCount = ipPathAccessStat.count.get();
        long timeCost = ipPathAccessStat.timeCost.get();
        long duration = ipPathAccessStat.getDuration();
        ret.setAppType(Byte.valueOf(this.appType));
        ret.setRequestCount(Integer.valueOf(requestCount));
        ret.setTimeCost(Long.valueOf(timeCost));
        ret.setTimeCostPerRequest(Double.valueOf(timeCost / requestCount));
        ret.setDuration(Long.valueOf(duration));
        ret.setDurationType(ipPathAccessStat.getDurationType());
        ret.setFirstAccessTime(new Date(ipPathAccessStat.getFirstAccessTime()));
        ret.setFormatedDuration(ipPathAccessStat.getFormatedDuration());
        ret.setIp(ipPathAccessStat.getIp());
        ret.setIpid(IpInfoService.ME.save(ipPathAccessStat.getIp()).getId());
        ret.setNoSessionCount(Integer.valueOf(ipPathAccessStat.noSessionCount.get()));
        ret.setPath(ipPathAccessStat.getPath());
        if (Objects.equals(Byte.valueOf(this.appType), (byte) 8)) {
            ret.setRestype("api");
        } else {
            String ext = FileUtil.extName(ipPathAccessStat.getPath());
            if (StrUtil.isNotBlank(ext)) {
                ret.setRestype(ext);
            }
        }
        ret.setRequestCountPerSecond(Double.valueOf(ipPathAccessStat.getPerSecond()));
        ret.setServer(Const.MY_IP_API);
        return ret;
    }

    public void onExpired(TioConfig tioConfig, String ip, IpAccessStat ipAccessStat) {
        IpPathAccessStat ipPathAccessStat;
        String ext;
        if (Objects.equals(ipAccessStat.getDurationType(), Long.valueOf(Const.IpPathAccessStatDuration.DURATION_2))) {
            TioIpPathAccessStat tioIpAccessStat = toDbObj(ipAccessStat);
            TioIpPathAccessStatService.ME.save(tioIpAccessStat);
            MapWithLock<String, IpPathAccessStat> ipPathAccessStatMap = ipAccessStat.getIpPathAccessStatMap();
            if (ipPathAccessStatMap != null) {
                ReentrantReadWriteLock.ReadLock readLock = ipPathAccessStatMap.readLock();
                readLock.lock();
                try {
                    try {
                        Map<String, IpPathAccessStat> map = (Map) ipPathAccessStatMap.getObj();
                        if (map != null) {
                            Set<Map.Entry<String, IpPathAccessStat>> set = map.entrySet();
                            if (set.size() > 0) {
                                List<TioIpPathAccessStat> modelList = new ArrayList<>();
                                for (Map.Entry<String, IpPathAccessStat> entry : set) {
                                    try {
                                        ipPathAccessStat = entry.getValue();
                                        String path = ipPathAccessStat.getPath();
                                        ext = FileUtil.extName(path);
                                    } catch (Exception e) {
                                        log.error(Json.toFormatedJson((Object) null), e);
                                    }
                                    if (!skipExtSet.contains(ext)) {
                                        TioIpPathAccessStat tioIpPathAccessStat = toDbObj(ipPathAccessStat);
                                        modelList.add(tioIpPathAccessStat);
                                    }
                                }
                                TioIpPathAccessStatService.ME.batchSave(modelList);
                            }
                        }
                        readLock.unlock();
                    } catch (Throwable e2) {
                        log.error(e2.toString(), e2);
                        readLock.unlock();
                    }
                } catch (Throwable th) {
                    readLock.unlock();
                    throw th;
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("ip:{}, \r\nipAccessStat:{} ", ip, Json.toFormatedJson(ipAccessStat));
            }
        }
    }

    public boolean onChanged(HttpRequest httpRequest, String ip, String path, IpAccessStat ipAccessStat, IpPathAccessStat ipPathAccessStat) {
        if (IpWhiteListService.isWhiteIp(ip)) {
            return true;
        }
        int allcount = ipAccessStat.count.get();
        if (allcount % STEP != 0) {
            return true;
        }
        log.debug("ip:{}, path:{}", ip, path);
        double preSecond = ipAccessStat.getPerSecond();
        int noSessionCount = ipAccessStat.noSessionCount.get();
        if (noSessionCount > MAX_NO_SESSION_COUNT) {
            String remark = "不带cookie的请求次数达到上限[" + MAX_NO_SESSION_COUNT + "]";
            pullBlack(httpRequest, ipAccessStat, ipPathAccessStat, remark);
            return false;
        }
        if (ipAccessStat.sessionIds.size() > MAX_SESSION_COUNT) {
            String remark2 = "ip访问违反条件：不同sessionid的数量达到上限[" + MAX_SESSION_COUNT + "]";
            pullBlack(httpRequest, ipAccessStat, ipPathAccessStat, remark2);
            return false;
        }
        boolean g1_c1 = allcount > G1_MAX_COUNT;
        boolean g1_c2 = preSecond > ((double) G1_MAX_PER_SECOND);
        boolean g1 = g1_c1 && g1_c2;
        if (g1) {
            String remark3 = "ip访问违反条件组1：总访问次数" + allcount + "(> " + G1_MAX_COUNT + "), 平均每秒访问" + preSecond + "次 (>" + G1_MAX_PER_SECOND + ")";
            pullBlack(httpRequest, ipAccessStat, ipPathAccessStat, remark3);
            return false;
        }
        boolean g2_c1 = allcount > G2_MAX_COUNT;
        boolean g2_c2 = preSecond > ((double) G2_MAX_PER_SECOND);
        boolean g2 = g2_c1 && g2_c2;
        if (g2) {
            String remark4 = "ip访问违反条件组2：总访问次数" + allcount + "(> " + G2_MAX_COUNT + "), 平均每秒访问" + preSecond + "次 (>" + G2_MAX_PER_SECOND + ")";
            pullBlack(httpRequest, ipAccessStat, ipPathAccessStat, remark4);
            return false;
        }
        String sessionId = DefaultHttpRequestHandler.getSessionId(httpRequest);
        if (StrUtil.isNotBlank(sessionId)) {
            boolean isOk = WebApiHttpSessionListener.isValidSessionId(sessionId);
            if (!isOk) {
            }
            return true;
        }
        return true;
    }

    public void pullBlack(HttpRequest request, IpAccessStat ipAccessStat, IpPathAccessStat ipPathAccessStat, String remark) {
        TioIpPathAccessStat tioIpAccessStat = toDbObj(ipAccessStat);
        TioIpPathAccessStatService.ME.save(tioIpAccessStat);
        TioIpPullblackUtils.addToBlack(request, request.getClientIp(), remark, (byte) 1);
        request.close();
    }

    public static void main(String[] args) {
        System.out.println(3);
        System.out.println(0);
    }
}
