package org.tio.p000mg.web.server.http.stat.token;

import org.tio.http.common.HttpRequest;
import org.tio.http.server.intf.CurrUseridGetter;
import org.tio.p000mg.web.server.utils.WebUtils;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/stat/token/TioSiteCurrUseridGetter.class */
public class TioSiteCurrUseridGetter implements CurrUseridGetter {

    /* renamed from: me */
    public static TioSiteCurrUseridGetter f5me = new TioSiteCurrUseridGetter();

    private TioSiteCurrUseridGetter() {
    }

    public String getUserid(HttpRequest request) {
        Integer ret = WebUtils.currUserId(request);
        if (ret != null) {
            return ret + "";
        }
        return null;
    }

    public static void main(String[] args) {
    }
}
