package org.tio.p000mg.web.server.http.stat.token;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.TioConfig;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.handler.DefaultHttpRequestHandler;
import org.tio.http.server.stat.token.TokenAccessStat;
import org.tio.http.server.stat.token.TokenPathAccessStat;
import org.tio.http.server.stat.token.TokenPathAccessStatListener;
import org.tio.mg.service.model.stat.TioTokenPathAccessStat;
import org.tio.mg.service.service.base.IpInfoService;
import org.tio.mg.service.service.base.TioTokenPathAccessStatService;
import org.tio.mg.service.service.conf.IpWhiteListService;
import org.tio.mg.service.service.conf.MgConfService;
import org.tio.p000mg.web.server.http.WebApiHttpSessionListener;
import org.tio.p000mg.web.server.utils.TioIpPullblackUtils;
import org.tio.sitexxx.servicecommon.vo.Const;
import org.tio.utils.json.Json;
import org.tio.utils.lock.MapWithLock;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/http/stat/token/TioSiteTokenPathAccessStatListener.class */
public class TioSiteTokenPathAccessStatListener implements TokenPathAccessStatListener {
    private byte appType;
    private static final String CONF_PREFIX = "token.access.";
    private static final int STEP;
    private static final int G1_MAX_COUNT;
    private static final int G1_MAX_PER_SECOND;
    private static final int G2_MAX_COUNT;
    private static final int G2_MAX_PER_SECOND;
    private static Logger log = LoggerFactory.getLogger(TioSiteTokenPathAccessStatListener.class);
    public static final TioSiteTokenPathAccessStatListener ME_SITE_VIEW = new TioSiteTokenPathAccessStatListener((byte) 9);
    public static final TioSiteTokenPathAccessStatListener ME_SITE_API = new TioSiteTokenPathAccessStatListener((byte) 8);
    private static Set<String> skipExtSet = new HashSet();

    static {
        skipExtSet.add("css");
        skipExtSet.add("js");
        skipExtSet.add("ico");
        skipExtSet.add("png");
        skipExtSet.add("jpg");
        skipExtSet.add("swf");
        skipExtSet.add("xml");
        skipExtSet.add("gif");
        skipExtSet.add("jpeg");
        skipExtSet.add("woff");
        skipExtSet.add("map");
        skipExtSet.add("txt");
        skipExtSet.add("mp4");
        skipExtSet.add("m3u8");
        skipExtSet.add("svg");
        STEP = MgConfService.getInt("token.access.STEP", 200).intValue();
        G1_MAX_COUNT = MgConfService.getInt("token.access.G1_MAX_COUNT", 200).intValue();
        G1_MAX_PER_SECOND = MgConfService.getInt("token.access.G1_MAX_PER_SECOND", 5).intValue();
        G2_MAX_COUNT = MgConfService.getInt("token.access.G2_MAX_COUNT", 100).intValue();
        G2_MAX_PER_SECOND = MgConfService.getInt("token.access.G2_MAX_PER_SECOND", 10).intValue();
    }

    public TioTokenPathAccessStat toDbObj(TokenAccessStat tokenAccessStat) {
        TioTokenPathAccessStat ret = new TioTokenPathAccessStat();
        int requestCount = tokenAccessStat.count.get();
        long timeCost = tokenAccessStat.timeCost.get();
        long duration = tokenAccessStat.getDuration();
        String uidStr = tokenAccessStat.getUid();
        if (StrUtil.isNotBlank(uidStr)) {
            ret.setUid(Integer.valueOf(Integer.parseInt(uidStr)));
        }
        ret.setToken(tokenAccessStat.getToken());
        ret.setAppType(Byte.valueOf(this.appType));
        ret.setRequestCount(Integer.valueOf(requestCount));
        ret.setTimeCost(Long.valueOf(timeCost));
        ret.setTimeCostPerRequest(Double.valueOf(timeCost / requestCount));
        ret.setDuration(Long.valueOf(duration));
        ret.setDurationType(tokenAccessStat.getDurationType());
        ret.setFirstAccessTime(new Date(tokenAccessStat.getFirstAccessTime()));
        ret.setFormatedDuration(tokenAccessStat.getFormatedDuration());
        ret.setIp(tokenAccessStat.getIp());
        ret.setIpid(IpInfoService.ME.save(tokenAccessStat.getIp()).getId());
        ret.setPath((String) null);
        ret.setRequestCountPerSecond(Double.valueOf(tokenAccessStat.getPerSecond()));
        ret.setServer(Const.MY_IP_API);
        return ret;
    }

    public TioTokenPathAccessStat toDbObj(TokenPathAccessStat tokenPathAccessStat) {
        TioTokenPathAccessStat ret = new TioTokenPathAccessStat();
        int requestCount = tokenPathAccessStat.count.get();
        long timeCost = tokenPathAccessStat.timeCost.get();
        long duration = tokenPathAccessStat.getDuration();
        String uidStr = tokenPathAccessStat.getUid();
        if (StrUtil.isNotBlank(uidStr)) {
            ret.setUid(Integer.valueOf(Integer.parseInt(uidStr)));
        }
        ret.setToken(tokenPathAccessStat.getToken());
        ret.setAppType(Byte.valueOf(this.appType));
        ret.setRequestCount(Integer.valueOf(requestCount));
        ret.setTimeCost(Long.valueOf(timeCost));
        ret.setTimeCostPerRequest(Double.valueOf(timeCost / requestCount));
        ret.setDuration(Long.valueOf(duration));
        ret.setDurationType(tokenPathAccessStat.getDurationType());
        ret.setFirstAccessTime(new Date(tokenPathAccessStat.getFirstAccessTime()));
        ret.setFormatedDuration(tokenPathAccessStat.getFormatedDuration());
        ret.setIp(tokenPathAccessStat.getIp());
        ret.setIpid(IpInfoService.ME.save(tokenPathAccessStat.getIp()).getId());
        ret.setPath(tokenPathAccessStat.getPath());
        if (Objects.equals(Byte.valueOf(this.appType), (byte) 8)) {
            ret.setRestype("api");
        } else {
            String ext = FileUtil.extName(tokenPathAccessStat.getPath());
            if (StrUtil.isNotBlank(ext)) {
                ret.setRestype(ext);
            }
        }
        ret.setRequestCountPerSecond(Double.valueOf(tokenPathAccessStat.getPerSecond()));
        ret.setServer(Const.MY_IP_API);
        return ret;
    }

    public TioSiteTokenPathAccessStatListener(byte appType) {
        this.appType = (byte) 9;
        this.appType = appType;
    }

    public void onExpired(TioConfig tioConfig, String token, TokenAccessStat tokenAccessStat) {
        TokenPathAccessStat tokenPathAccessStat;
        String ext;
        if (Objects.equals(tokenAccessStat.getDurationType(), Long.valueOf(Const.TokenPathAccessStatDuration.DURATION_2))) {
            TioTokenPathAccessStat tioTokenAccessStat = toDbObj(tokenAccessStat);
            TioTokenPathAccessStatService.ME.save(tioTokenAccessStat);
            MapWithLock<String, TokenPathAccessStat> tokenPathAccessStatMap = tokenAccessStat.getTokenPathAccessStatMap();
            if (tokenPathAccessStatMap != null) {
                ReentrantReadWriteLock.ReadLock readLock = tokenPathAccessStatMap.readLock();
                readLock.lock();
                try {
                    try {
                        Map<String, TokenPathAccessStat> map = (Map) tokenPathAccessStatMap.getObj();
                        if (map != null) {
                            Set<Map.Entry<String, TokenPathAccessStat>> set = map.entrySet();
                            if (set.size() > 0) {
                                List<TioTokenPathAccessStat> modelList = new ArrayList<>();
                                for (Map.Entry<String, TokenPathAccessStat> entry : set) {
                                    try {
                                        tokenPathAccessStat = entry.getValue();
                                        String path = tokenPathAccessStat.getPath();
                                        ext = FileUtil.extName(path);
                                    } catch (Exception e) {
                                        log.error(Json.toFormatedJson((Object) null), e);
                                    }
                                    if (!skipExtSet.contains(ext)) {
                                        TioTokenPathAccessStat tioTokenPathAccessStat = toDbObj(tokenPathAccessStat);
                                        modelList.add(tioTokenPathAccessStat);
                                    }
                                }
                                TioTokenPathAccessStatService.ME.batchSave(modelList);
                            }
                        }
                        readLock.unlock();
                    } catch (Throwable e2) {
                        log.error(e2.toString(), e2);
                        readLock.unlock();
                    }
                } catch (Throwable th) {
                    readLock.unlock();
                    throw th;
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("token:{}, \r\ntokenAccessStat:{} ", token, Json.toFormatedJson(tokenAccessStat));
            }
        }
    }

    public boolean onChanged(HttpRequest httpRequest, String token, String path, TokenAccessStat tokenAccessStat, TokenPathAccessStat tokenPathAccessStat) {
        if (IpWhiteListService.isWhiteIp(httpRequest.getClientIp())) {
            return true;
        }
        int allcount = tokenAccessStat.count.get();
        if (allcount % STEP != 0) {
            return true;
        }
        log.debug("token:{}, path:{}", token, path);
        String ip1 = tokenAccessStat.getIp();
        String ip = httpRequest.getClientIp();
        if (!StrUtil.equals(ip1, ip)) {
        }
        double preSecond = tokenAccessStat.getPerSecond();
        boolean g1_c1 = allcount > G1_MAX_COUNT;
        boolean g1_c2 = preSecond > ((double) G1_MAX_PER_SECOND);
        boolean g1 = g1_c1 && g1_c2;
        if (g1) {
            String remark = "token【" + token + "】访问违反条件组1：总访问次数" + allcount + "( > " + G1_MAX_COUNT + "), 平均每秒访问" + preSecond + "次 (>" + G1_MAX_PER_SECOND + ")，拉黑时访问的地址是：【" + path + "】";
            pullBlack(httpRequest, tokenAccessStat, tokenPathAccessStat, remark);
            return false;
        }
        boolean g2_c1 = allcount > G2_MAX_COUNT;
        boolean g2_c2 = preSecond > ((double) G2_MAX_PER_SECOND);
        boolean g2 = g2_c1 && g2_c2;
        if (g2) {
            String remark2 = "token【" + token + "】访问违反条件组2：总访问次数" + allcount + "( > " + G2_MAX_COUNT + "), 平均每秒访问" + preSecond + "次 (>" + G2_MAX_PER_SECOND + ")，拉黑时访问的地址是：【" + path + "】";
            pullBlack(httpRequest, tokenAccessStat, tokenPathAccessStat, remark2);
            return false;
        }
        String sessionId = DefaultHttpRequestHandler.getSessionId(httpRequest);
        if (StrUtil.isNotBlank(sessionId)) {
            boolean isOk = WebApiHttpSessionListener.isValidSessionId(sessionId);
            if (!isOk) {
            }
            return true;
        }
        return true;
    }

    public void pullBlack(HttpRequest request, TokenAccessStat tokenAccessStat, TokenPathAccessStat tokenPathAccessStat, String remark) {
        TioTokenPathAccessStat tioTokenAccessStat = toDbObj(tokenAccessStat);
        TioTokenPathAccessStatService.ME.save(tioTokenAccessStat);
        TioIpPullblackUtils.addToBlack(request, request.getClientIp(), remark, (byte) 1);
        request.close();
    }

    public static void main(String[] args) {
    }

    public byte getAppType() {
        return this.appType;
    }

    public void setAppType(byte appType) {
        this.appType = appType;
    }
}
