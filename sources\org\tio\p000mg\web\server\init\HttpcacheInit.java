package org.tio.p000mg.web.server.init;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.server.mvc.Routes;
import org.tio.mg.service.model.conf.Httpcache;
import org.tio.mg.service.service.conf.HttpcacheService;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/init/HttpcacheInit.class */
public class HttpcacheInit {
    private static Logger log = LoggerFactory.getLogger(HttpcacheInit.class);

    public static void init(Routes routes) {
        List<Httpcache> list = HttpcacheService.getAll();
        if (CollectionUtil.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder(50);
            for (Httpcache httpcache : list) {
                String path = httpcache.getPath();
                if (!routes.PATH_METHOD_MAP.containsKey(path)) {
                    sb.append(path).append(System.lineSeparator());
                }
            }
            if (sb.length() > 0) {
                log.error("有些路径在httpcache表中配置了，但是是无效路径\r\n{}", sb.toString());
                try {
                    String writeMappingToFile = System.getProperty("tio.mvc.route.writeMappingToFile", "true");
                    if ("true".equalsIgnoreCase(writeMappingToFile)) {
                        FileUtil.writeUtf8String("有些路径在httpcache表中配置了，但是是无效路径\r\n" + sb.toString(), "/tio_error_httpcache_path.txt");
                    }
                } catch (Exception e) {
                    log.error(e.toString(), e);
                }
            }
        }
    }
}
