package org.tio.p000mg.web.server.init;

import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.tio.mg.service.init.RedisInit;
import org.tio.p000mg.web.server.topic.TopicClearHttpCacheListener;
import org.tio.p000mg.web.server.topic.TopicPullIpToBlackListener;
import org.tio.sitexxx.servicecommon.vo.ClearHttpCache;
import org.tio.sitexxx.servicecommon.vo.topic.PullIpToBlackVo;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/init/TopicInit.class */
public class TopicInit {
    public static void init() {
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("CLEAR_HTTP_CACHE");
        topic.addListener(ClearHttpCache.class, TopicClearHttpCacheListener.f7me);
        RTopic pullIpToBlackTopic = redisson.getTopic("PULL_IP_TO_BLACK");
        pullIpToBlackTopic.addListener(PullIpToBlackVo.class, TopicPullIpToBlackListener.f8me);
    }
}
