package org.tio.p000mg.web.server.topic;

import cn.hutool.core.util.StrUtil;
import java.util.Map;
import org.redisson.api.listener.MessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.p000mg.web.server.http.WebApiHttpServerInterceptor;
import org.tio.sitexxx.servicecommon.vo.ClearHttpCache;
import org.tio.utils.json.Json;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/topic/TopicClearHttpCacheListener.class */
public class TopicClearHttpCacheListener implements MessageListener<ClearHttpCache> {
    private static Logger log = LoggerFactory.getLogger(TopicClearHttpCacheListener.class);

    /* renamed from: me */
    public static final TopicClearHttpCacheListener f7me = new TopicClearHttpCacheListener();

    public static void main(String[] args) {
    }

    public void onMessage(CharSequence channel, ClearHttpCache clearHttpCache) {
        String path = clearHttpCache.getPath();
        if (StrUtil.isBlank(path)) {
            return;
        }
        int clearType = clearHttpCache.getClearType();
        if (clearType == 2) {
            WebApiHttpServerInterceptor.clearHttpCache(path);
        } else {
            Map<String, Object> param = clearHttpCache.getParam();
            Integer userid = clearHttpCache.getUserid();
            WebApiHttpServerInterceptor.removeHttpCache(path, param, userid);
        }
        if (log.isInfoEnabled()) {
            log.info("收到通知，已经清除httpcache[{}], clearHttpCache:{}", path, Json.toJson(clearHttpCache));
        }
    }
}
