package org.tio.p000mg.web.server.topic;

import cn.hutool.core.util.StrUtil;
import java.util.Objects;
import org.redisson.api.listener.MessageListener;
import org.tio.core.Tio;
import org.tio.p000mg.web.server.init.WebApiInit;
import org.tio.sitexxx.servicecommon.vo.topic.PullIpToBlackVo;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/topic/TopicPullIpToBlackListener.class */
public class TopicPullIpToBlackListener implements MessageListener<PullIpToBlackVo> {

    /* renamed from: me */
    public static final TopicPullIpToBlackListener f8me = new TopicPullIpToBlackListener();

    public void onMessage(CharSequence channel, PullIpToBlackVo pullIpToBlackVo) {
        String ip = pullIpToBlackVo.getIp();
        if (StrUtil.isNotBlank(ip)) {
            if (Objects.equals(pullIpToBlackVo.getType(), PullIpToBlackVo.Type.ADD_BLACK_IP)) {
                Tio.IpBlacklist.add(WebApiInit.serverTioConfig, ip);
            } else {
                Tio.IpBlacklist.remove(WebApiInit.serverTioConfig, ip);
            }
        }
    }
}
