package org.tio.p000mg.web.server.utils;

import com.alibaba.fastjson.JSONObject;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/utils/HttpRequestUtils.class */
public class HttpRequestUtils {
    private static HttpRequestUtils instance = null;

    private HttpRequestUtils() {
    }

    public static HttpRequestUtils getInstance() {
        if (instance == null) {
            synchronized (HttpRequestUtils.class) {
                if (instance == null) {
                    instance = new HttpRequestUtils();
                }
            }
        }
        return instance;
    }

    public String sendPost(String url, Map<String, Object> paramsMap) throws IOException {
        try {
            JSONObject param = new JSONObject();
            for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
                param.put(entry.getKey(), entry.getValue());
            }
            StringBuilder sb = new StringBuilder();
            URL urlObj = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
            conn.setUseCaches(false);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8));
            Throwable th = null;
            try {
                try {
                    writer.write(param.toJSONString());
                    writer.flush();
                    if (writer != null) {
                        if (0 != 0) {
                            try {
                                writer.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            writer.close();
                        }
                    }
                    BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                    Throwable th3 = null;
                    while (true) {
                        try {
                            try {
                                String tmpLine = reader.readLine();
                                if (tmpLine == null) {
                                    break;
                                }
                                sb.append(tmpLine);
                            } finally {
                            }
                        } finally {
                        }
                    }
                    if (reader != null) {
                        if (0 != 0) {
                            try {
                                reader.close();
                            } catch (Throwable th4) {
                                th3.addSuppressed(th4);
                            }
                        } else {
                            reader.close();
                        }
                    }
                    conn.disconnect();
                    return sb.toString();
                } finally {
                }
            } finally {
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getUrlParams(Map<String, Object> paramsMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
            sb.append(entry.getKey() + "=" + entry.getValue() + "&");
        }
        String result = sb.toString();
        int i = result.lastIndexOf("&");
        return result.substring(0, i);
    }
}
