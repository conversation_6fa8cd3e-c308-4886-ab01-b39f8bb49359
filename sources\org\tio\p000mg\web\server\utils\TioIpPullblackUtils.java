package org.tio.p000mg.web.server.utils;

import java.util.Date;
import org.tio.http.common.HttpRequest;
import org.tio.mg.service.model.stat.TioIpPullblackLog;
import org.tio.mg.service.service.base.IpInfoService;
import org.tio.mg.service.service.base.TioIpPullblackLogService;
import org.tio.mg.service.service.conf.IpBlackListService;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/utils/TioIpPullblackUtils.class */
public class TioIpPullblackUtils {
    public static void main(String[] args) {
    }

    public static void addToBlack(HttpRequest request, String ip, String remark, byte type) {
        Integer currId = WebUtils.currUserId(request);
        TioIpPullblackLog tioIpPullblackLog = new TioIpPullblackLog();
        tioIpPullblackLog.setIp(ip);
        tioIpPullblackLog.setIpid(IpInfoService.ME.save(request.getClientIp()).getId());
        tioIpPullblackLog.setRemark(remark);
        tioIpPullblackLog.setServer(Const.MY_IP_API);
        tioIpPullblackLog.setServerport(Integer.valueOf(request.getChannelContext().getServerNode().getPort()));
        tioIpPullblackLog.setTime(new Date());
        tioIpPullblackLog.setType(Byte.valueOf(type));
        tioIpPullblackLog.setSessionid(request.getHttpSession().getId());
        tioIpPullblackLog.setCookie(request.getHeader("cookie"));
        tioIpPullblackLog.setInitpath(request.requestLine.getInitPath());
        tioIpPullblackLog.setPath(request.requestLine.getPath());
        tioIpPullblackLog.setRequestline(request.requestLine.toString());
        tioIpPullblackLog.setUid(currId);
        TioIpPullblackLogService.ME.addToBlack(tioIpPullblackLog);
        IpBlackListService.me.save(ip, remark);
    }
}
