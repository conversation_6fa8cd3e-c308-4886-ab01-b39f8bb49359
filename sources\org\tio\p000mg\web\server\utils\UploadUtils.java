package org.tio.p000mg.web.server.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import java.io.File;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.mg.service.model.mg.MgInvoice;
import org.tio.p000mg.web.server.init.WebApiInit;
import org.tio.sitexxx.servicecommon.vo.Const;

/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/utils/UploadUtils.class */
public class UploadUtils {
    private static Logger log = LoggerFactory.getLogger(UploadUtils.class);

    public static String dataRootPath(String subDir, int uid) {
        long uid1 = uid + 74541287548L;
        long dir1 = uid1 % 107;
        long dir2 = uid1 % (107 * 107);
        long dir3 = uid1 % ((107 * 107) * 107);
        long dir4 = uid1 % (((107 * 107) * 107) * 107);
        String path = "/" + subDir + "/" + dir1 + "/" + dir2 + "/" + dir3 + "/" + dir4 + "/" + uid1;
        return path;
    }

    public static String newFile(String subDir, int uid, String filename) {
        String path = dataRootPath(subDir, uid);
        Date date = new Date();
        String path2 = path + "/" + RandomUtil.randomInt(1, 120);
        String resRootDir = Const.RES_ROOT;
        File dir = new File(resRootDir, path2);
        FileUtil.mkdir(dir);
        String fileName = DateUtil.format(date, "HHmmss");
        if (filename != null) {
            fileName = fileName + "/" + WebApiInit.sessionIdGenerator.nextId();
        }
        return path2 + "/" + fileName;
    }

    public static String mgSubDir(Byte type) {
        switch (type.byteValue()) {
            case 1:
                return "recruit/cmp/logo";
            case 2:
                return "order/contract";
            case 3:
                return "app/file";
            default:
                return "mg/default";
        }
    }

    public static String dateFile(String subDir) {
        Date date = new Date();
        String path = (("/" + subDir + "/") + DateUtil.format(new Date(), "yyyyMMdd")) + RandomUtil.randomInt(1, 120);
        String resRootDir = Const.RES_ROOT;
        File dir = new File(resRootDir, path);
        FileUtil.mkdir(dir);
        return path + "/" + DateUtil.format(date, "HHmmss") + WebApiInit.sessionIdGenerator.nextId();
    }

    public static String invoice(String subDir, String nick, MgInvoice invoice) {
        if (StrUtil.isBlank(nick)) {
            nick = "缺省名称";
        }
        double amonut = invoice.getAmount().doubleValue();
        if (amonut <= 0.0d) {
            amonut = 0.0d;
        }
        String path = subDir + "/" + nick + "/";
        String resRootDir = Const.RES_ROOT;
        File dir = new File(resRootDir, path);
        FileUtil.mkdir(dir);
        String fileName = nick;
        String fileName2 = (fileName + "-" + DateUtil.format(new Date(), "yyMMddHHmmssSSS")) + "-" + invoice.getName() + "￥" + amonut;
        Pattern pattern = Pattern.compile("[\\s\\\\/:\\*\\?\\\"<>\\|]");
        Matcher matcher = pattern.matcher(fileName2);
        String fileName3 = matcher.replaceAll("");
        return path + "/" + fileName3;
    }

    public static void main(String[] args) {
    }
}
