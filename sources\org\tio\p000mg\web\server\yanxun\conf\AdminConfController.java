package org.tio.p000mg.web.server.yanxun.conf;

import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.init.RedisInit;
import org.tio.mg.service.model.conf.YxConf;
import org.tio.mg.service.service.conf.MgConfService;
import org.tio.mg.service.service.yxnxun.conf.AdminConfService;
import org.tio.mg.service.service.yxnxun.conf.UserMemberService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.ConfigAllSaveVo;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.sitexxx.servicecommon.vo.topic.TopicVo;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/conf")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/conf/AdminConfController.class */
public class AdminConfController {
    private Logger log = LoggerFactory.getLogger(AdminConfController.class);
    private AdminConfService service = AdminConfService.ME;

    @RequestPath("/add")
    public Resp add(HttpRequest request) throws Exception {
        YxConf yxConf = (YxConf) JSONObject.parseObject(request.getBodyString(), YxConf.class);
        if (yxConf == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = checkConf(yxConf);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        if (!this.service.add(yxConf)) {
            return Resp.fail("操作失败");
        }
        clearConfigCache();
        return Resp.ok("操作成功");
    }

    public void clearConfigCache() {
        RedissonClient redissonClient = RedisInit.get();
        RTopic topic = redissonClient.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 3);
        topic.publish(topicVo);
    }

    @RequestPath("/del")
    public Resp del(HttpRequest request, Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        if (!this.service.del(id)) {
            return Resp.fail("操作失败");
        }
        clearConfigCache();
        return Resp.ok("操作成功");
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) {
        final YxConf yxConf = (YxConf) JSONObject.parseObject(request.getBodyString(), YxConf.class);
        if (yxConf == null || yxConf.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        yxConf.setUpdatetime(new Date());
        boolean update = yxConf.update();
        if (update) {
            if (yxConf.getConftype().byteValue() == 5) {
                List<YxConf> yxConfs = new ArrayList<>();
                yxConfs.add(yxConf);
                UserMemberService.ME.update(yxConfs);
            }
            clearConfigCache();
            new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.conf.AdminConfController.1
                @Override // java.lang.Runnable
                public void run() {
                    MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "修改单个参数", (byte) 2, "修改单个参数" + yxConf.getName());
                    MgConfService.clearCache();
                }
            }).start();
            return Resp.ok();
        }
        return Resp.fail("修改失败");
    }

    @RequestPath("/updateAll")
    public Resp updateAll(final HttpRequest request) throws Exception {
        ConfigAllSaveVo configAllSaveVo = (ConfigAllSaveVo) JSONObject.parseObject(request.getBodyString(), ConfigAllSaveVo.class);
        if (configAllSaveVo == null) {
            return Resp.fail("无效参数");
        }
        List<YxConf> yxConfs = configAllSaveVo.getConfs();
        Ret ret = this.service.updateAll(yxConfs);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        if (null != configAllSaveVo.getConfType() && configAllSaveVo.getConfType().byteValue() == 5) {
            UserMemberService.ME.update(yxConfs);
        }
        clearConfigCache();
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.conf.AdminConfController.2
            @Override // java.lang.Runnable
            public void run() {
                MgConfService.clearCache();
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "修改参数配置", (byte) 2, "修改参数配置");
                System.out.println("通知清除系统参数ALL");
            }
        }).start();
        return Resp.ok("修改成功");
    }

    @RequestPath("/queryList")
    public Resp queryList(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        if (requestVo == null || requestVo.getType() == null || requestVo.getType().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        List<YxConf> confs = this.service.queryByType(requestVo);
        return Resp.ok(confs);
    }

    @RequestPath("/getByName")
    public Resp getByName(HttpRequest request, String configName) {
        if (StrUtil.isEmpty(configName)) {
            return Resp.fail("无效参数");
        }
        YxConf string = this.service.findByName(configName);
        if (string == null) {
            return Resp.fail("参数不存在");
        }
        return Resp.ok(string);
    }

    private Ret checkConf(YxConf conf) {
        if (StrUtil.isBlank(conf.getName())) {
            return RetUtils.failMsg("参数名不可为空");
        }
        if (StrUtil.isBlank(conf.getValue())) {
            return RetUtils.failMsg("参数值不可为空");
        }
        if (StrUtil.isBlank(conf.getTitle())) {
            return RetUtils.failMsg("请填写参数说明");
        }
        if (conf.getConftype() == null) {
            return RetUtils.failMsg("请选择配置类型");
        }
        if (conf.getType() == null) {
            return RetUtils.failMsg("请选择参数展示类型");
        }
        YxConf confByName = this.service.findByName(conf.getName());
        if (confByName != null) {
            return RetUtils.failMsg("参数名已存在");
        }
        return RetUtils.okOper();
    }
}
