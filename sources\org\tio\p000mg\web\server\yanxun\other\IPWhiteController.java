package org.tio.p000mg.web.server.yanxun.other;

import com.alibaba.fastjson.JSONObject;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.conf.IpWhiteListService;
import org.tio.mg.service.service.tioim.TioUserService;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/ipwhite")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/other/IPWhiteController.class */
public class IPWhiteController {
    private TioUserService userService = TioUserService.me;
    private IpWhiteListService ipService = IpWhiteListService.me;

    @RequestPath("/queryList")
    public Resp query(HttpRequest request) {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.userService.queryIpWhite(requestVo);
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/addIps")
    public Resp addIPWhite(final HttpRequest request, Integer uid, final String ips) {
        if (uid == null || uid.intValue() < 0 || StrUtil.isEmpty(ips)) {
            return Resp.fail("无效参数");
        }
        String[] split = ips.split(",");
        Ret ret = this.ipService.saveIps(uid, split);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.other.IPWhiteController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "添加用户白名单", (byte) 1, "添加白名单，ips" + ips);
            }
        }).start();
        return Resp.ok("操作成功");
    }

    @RequestPath("/delIp")
    public Resp delIpWhite(final HttpRequest request, final Integer id) {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        if (this.ipService.delete(id, "") == 0) {
            return Resp.fail("操作失败");
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.other.IPWhiteController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "删除用户白名单", MgConst.OperLogTypes.del, "删除白名单，id为" + id);
            }
        }).start();
        return Resp.ok("操作成功");
    }
}
