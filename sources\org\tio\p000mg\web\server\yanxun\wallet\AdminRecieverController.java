package org.tio.p000mg.web.server.yanxun.wallet;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.main.YxAdminRecieverAccount;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.service.yxnxun.wallet.AdminRecieverService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/reciever")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/AdminRecieverController.class */
public class AdminRecieverController {
    private static Logger log = LoggerFactory.getLogger(AdminRecieverController.class);
    private static AdminRecieverService service = AdminRecieverService.ME;

    @RequestPath("/list")
    public Resp findAll(HttpRequest request) throws Exception {
        Ret ret = service.findAll();
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/findByid")
    public Resp findById(HttpRequest request) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(request.getBodyString());
        Integer id = -1;
        if (jsonObject != null) {
            id = (Integer) jsonObject.get("id");
        }
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = service.findById(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkData(ret));
    }

    @RequestPath("/save")
    public Resp save(final HttpRequest request) throws Exception {
        final YxAdminRecieverAccount yxAdminRecieverAccount = (YxAdminRecieverAccount) JSONObject.parseObject(request.getBodyString(), YxAdminRecieverAccount.class);
        if (yxAdminRecieverAccount == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = service.save(yxAdminRecieverAccount);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.AdminRecieverController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "新增收款人", (byte) 1, "新增收款人" + yxAdminRecieverAccount.getName());
            }
        }).start();
        return Resp.ok(RetUtils.getRetMsg(ret));
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) throws Exception {
        final YxAdminRecieverAccount yxAdminRecieverAccount = (YxAdminRecieverAccount) JSONObject.parseObject(request.getBodyString(), YxAdminRecieverAccount.class);
        if (yxAdminRecieverAccount == null || yxAdminRecieverAccount.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = service.update(yxAdminRecieverAccount);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.AdminRecieverController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "修改收款账户", (byte) 2, "修改收款账户" + yxAdminRecieverAccount.getName());
            }
        }).start();
        return Resp.ok(RetUtils.getRetMsg(ret));
    }

    @RequestPath("/del")
    public Resp del(final HttpRequest request) throws Exception {
        YxAdminRecieverAccount yxAdminRecieverAccount = (YxAdminRecieverAccount) JSONObject.parseObject(request.getBodyString(), YxAdminRecieverAccount.class);
        if (yxAdminRecieverAccount == null || yxAdminRecieverAccount.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        final Integer id = yxAdminRecieverAccount.getId();
        Ret ret = service.del(id);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.AdminRecieverController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "删除收款人", MgConst.OperLogTypes.del, "删除收款人" + id);
            }
        }).start();
        return Resp.ok(RetUtils.getRetMsg(ret));
    }
}
