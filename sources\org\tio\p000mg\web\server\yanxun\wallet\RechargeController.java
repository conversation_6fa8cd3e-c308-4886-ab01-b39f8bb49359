package org.tio.p000mg.web.server.yanxun.wallet;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.service.yxnxun.wallet.RechargeService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RechargeCheckVo;
import org.tio.mg.service.vo.RechargeRequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/recharge")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/RechargeController.class */
public class RechargeController {
    private static Logger log = LoggerFactory.getLogger(RechargeController.class);
    private static RechargeService service = RechargeService.ME;

    @RequestPath("/list")
    public Resp queryRechargeList(HttpRequest request) throws Exception {
        RechargeRequestVo rechargeRequestVo = (RechargeRequestVo) JSONObject.parseObject(request.getBodyString(), RechargeRequestVo.class);
        if (rechargeRequestVo == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = service.queryRecharge(rechargeRequestVo);
        if (ret.isFail()) {
            log.error("查询支付列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/check")
    public Resp checkRecharge(final HttpRequest request) throws Exception {
        final RechargeCheckVo rechargeCheckVo = (RechargeCheckVo) JSONObject.parseObject(request.getBodyString(), RechargeCheckVo.class);
        if (rechargeCheckVo == null || rechargeCheckVo.getId() == null || rechargeCheckVo.getStatus() == null) {
            return Resp.fail("无效参数");
        }
        Ret statusSame = service.isStatusSame(rechargeCheckVo.getId(), rechargeCheckVo.getStatus());
        if (statusSame.isFail()) {
            log.error(RetUtils.getRetMsg(statusSame));
            return Resp.fail(RetUtils.getRetMsg(statusSame));
        }
        MgUser mgUser = WebUtils.currUser(request);
        Ret ret = service.check(mgUser, rechargeCheckVo);
        if (ret.isFail()) {
            log.error(RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.RechargeController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "充值审核", (byte) 2, "充值审核结果" + rechargeCheckVo.getStatus());
            }
        }).start();
        return Resp.ok(RetUtils.getRetMsg(ret));
    }

    @RequestPath("/backRecharge")
    public Resp recharge(final HttpRequest request, final Integer money, final Integer uid) throws Exception {
        Integer mguid = WebUtils.currUserId(request);
        if (money == null || money.intValue() == 0) {
            return Resp.fail("充值金额无效");
        }
        if (uid == null) {
            return Resp.fail("用户id为空");
        }
        Ret ret = service.recharge(mguid, money, uid);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.RechargeController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台充值", (byte) 2, "后台充值  金额：" + money + ";uid:" + uid);
            }
        }).start();
        return Resp.ok().msg(RetUtils.getRetMsg(ret));
    }
}
