package org.tio.p000mg.web.server.yanxun.wallet;

import com.alibaba.fastjson.JSONObject;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.yxnxun.wallet.RedPacketService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.DeleteIdVo;
import org.tio.mg.service.vo.RedPacketVo;
import org.tio.utils.resp.Resp;

@RequestPath("/redPackage")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/RedPackageController.class */
public class RedPackageController {
    private RedPacketService redPacketService = RedPacketService.me;

    @RequestPath("/queryList")
    public Resp query(HttpRequest request) {
        RedPacketVo redPacketVo = (RedPacketVo) JSONObject.parseObject(request.getBodyString(), RedPacketVo.class);
        if (redPacketVo == null) {
            return Resp.ok("无效参数");
        }
        Ret ret = this.redPacketService.findList(redPacketVo);
        if (ret == null) {
            return Resp.ok("暂无数据");
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/detail")
    public Resp detail(HttpRequest request) {
        DeleteIdVo deleteIdVo = (DeleteIdVo) JSONObject.parseObject(request.getBodyString(), DeleteIdVo.class);
        if (deleteIdVo == null || deleteIdVo.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.redPacketService.findDetailsById(deleteIdVo.getId());
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }
}
