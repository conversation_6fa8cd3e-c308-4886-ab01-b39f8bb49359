package org.tio.p000mg.web.server.yanxun.wallet;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.yxnxun.wallet.TransAccountService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.TransferVo;
import org.tio.utils.resp.Resp;

@RequestPath("/transAccount")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/TransAccountsController.class */
public class TransAccountsController {
    private Logger log = LoggerFactory.getLogger(TransAccountsController.class);
    private TransAccountService service = TransAccountService.me;

    @RequestPath("/queryList")
    public Resp getTransAccountList(HttpRequest request) {
        TransferVo transferVo = (TransferVo) JSONObject.parseObject(request.getBodyString(), TransferVo.class);
        if (transferVo == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.service.queryList(transferVo);
        if (ret == null) {
            return Resp.ok("暂无数据");
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }
}
