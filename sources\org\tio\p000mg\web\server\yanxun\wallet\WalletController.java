package org.tio.p000mg.web.server.yanxun.wallet;

import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.model.mg.MgUser;
import org.tio.mg.service.service.yxnxun.wallet.WalletService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/wallet")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/WalletController.class */
public class WalletController {
    private static Logger log = LoggerFactory.getLogger(WalletController.class);
    private static WalletService service = WalletService.ME;

    @RequestPath("/list")
    public Resp queryList(HttpRequest request) throws Exception {
        Ret ret = service.queryList();
        if (ret.isFail()) {
            log.error("获取钱包用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/listbytime")
    public Resp queryListByTime(HttpRequest request, Date sTime, Date eTime) throws Exception {
        Ret ret = service.queryListByTime(sTime, eTime);
        if (ret.isFail()) {
            log.error("获取钱包用户列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkList(ret));
    }

    @RequestPath("/blockedAccount")
    public Resp blockedAccount(HttpRequest request, Integer uid, Integer status, String note) {
        MgUser mgUser = WebUtils.currUser(request);
        if (mgUser == null) {
            log.info("尚未登录不可操作");
            return Resp.fail("您尚未登录或登录超时，请重新登录");
        }
        Ret ret = service.updateStatusByUid(uid, status);
        if (ret.isFail()) {
            log.error("账户冻结失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getRetMsg(ret));
    }
}
