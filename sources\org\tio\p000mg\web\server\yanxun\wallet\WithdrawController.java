package org.tio.p000mg.web.server.yanxun.wallet;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.service.yxnxun.wallet.WithdrawService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.RechargeCheckVo;
import org.tio.mg.service.vo.WithdrawRequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.utils.resp.Resp;

@RequestPath("/withdraw")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/wallet/WithdrawController.class */
public class WithdrawController {
    private static Logger log = LoggerFactory.getLogger(WithdrawController.class);
    private static WithdrawService service = WithdrawService.ME;

    @RequestPath("/list")
    public Resp queryWithdrawList(HttpRequest request) {
        WithdrawRequestVo withdrawRequestVo = (WithdrawRequestVo) JSONObject.parseObject(request.getBodyString(), WithdrawRequestVo.class);
        if (withdrawRequestVo == null) {
            return Resp.fail("无效参数");
        }
        Ret ret = service.queryWithdraw(withdrawRequestVo);
        if (ret.isFail()) {
            log.error("查询提现列表失败：{}", RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }

    @RequestPath("/updateCheckStatus")
    public Resp checkWithdraw(final HttpRequest request) {
        final RechargeCheckVo rechargeCheckVo = (RechargeCheckVo) JSONObject.parseObject(request.getBodyString(), RechargeCheckVo.class);
        Ret statusSame = service.isStatusSame(rechargeCheckVo.getId(), rechargeCheckVo.getStatus());
        if (statusSame.isFail()) {
            log.error(RetUtils.getRetMsg(statusSame));
            return Resp.fail(RetUtils.getRetMsg(statusSame));
        }
        Integer mgUid = WebUtils.currUserId(request);
        Ret ret = service.check(mgUid, rechargeCheckVo.getId(), rechargeCheckVo.getUid(), rechargeCheckVo.getStatus(), rechargeCheckVo.getNote());
        if (ret.isFail()) {
            log.error(RetUtils.getRetMsg(ret));
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.WithdrawController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "提现审核", (byte) 2, "提现审核结果：" + rechargeCheckVo.getStatus() + ",备注：" + rechargeCheckVo.getNote());
            }
        }).start();
        return Resp.ok().msg(RetUtils.getRetMsg(ret));
    }

    @RequestPath("/backWithdraw")
    public Resp withdraw(final HttpRequest request, final Integer money, final Integer uid) throws Exception {
        Integer mguid = WebUtils.currUserId(request);
        if (money == null || money.intValue() == 0) {
            return Resp.fail("扣款金额无效");
        }
        if (uid == null) {
            return Resp.fail("用户id为空");
        }
        Ret ret = service.withdraw(mguid, money, uid);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.wallet.WithdrawController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "后台扣款", (byte) 2, "后台扣款，金额:" + money + ";uid:" + uid);
            }
        }).start();
        return Resp.ok().msg(RetUtils.getRetMsg(ret));
    }
}
