package org.tio.p000mg.web.server.yanxun.word;

import com.alibaba.fastjson.JSONObject;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.http.common.HttpRequest;
import org.tio.http.server.annotation.RequestPath;
import org.tio.jfinal.kit.Ret;
import org.tio.mg.service.init.RedisInit;
import org.tio.mg.service.model.conf.SensitiveWordsList;
import org.tio.mg.service.service.yxnxun.other.MgOperLogService;
import org.tio.mg.service.service.yxnxun.word.WordService;
import org.tio.mg.service.utils.RetUtils;
import org.tio.mg.service.vo.MgConst;
import org.tio.mg.service.vo.RequestVo;
import org.tio.p000mg.web.server.utils.MgOperRequestUtils;
import org.tio.p000mg.web.server.utils.WebUtils;
import org.tio.sitexxx.servicecommon.vo.topic.TopicVo;
import org.tio.utils.hutool.StrUtil;
import org.tio.utils.resp.Resp;

@RequestPath("/word")
/* loaded from: tio-mg-http-server-api-1.0.0-tio-mg.jar:org/tio/mg/web/server/yanxun/word/WordController.class */
public class WordController {
    private Logger log = LoggerFactory.getLogger(WordController.class);
    private WordService service = WordService.ME;

    @RequestPath("/add")
    public Resp add(final HttpRequest request) throws Exception {
        final SensitiveWordsList sensitiveWordsList = (SensitiveWordsList) JSONObject.parseObject(request.getBodyString(), SensitiveWordsList.class);
        if (sensitiveWordsList == null || StrUtil.isBlank(sensitiveWordsList.getWord())) {
            return Resp.fail("无效参数");
        }
        Integer mgUid = WebUtils.currUserId(request);
        sensitiveWordsList.setMguid(mgUid);
        Ret ret = this.service.add(sensitiveWordsList);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 10);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.word.WordController.1
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "添加敏感词", (byte) 1, "添加敏感词：" + sensitiveWordsList.getWord());
            }
        }).start();
        return Resp.ok("敏感词添加成功");
    }

    @RequestPath("/del")
    public Resp del(final HttpRequest request, final Integer id) throws Exception {
        if (id == null || id.intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Ret ret = this.service.del(id);
        if (ret.isFail()) {
            return Resp.fail("操作失败");
        }
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 10);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.word.WordController.2
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "删除敏感词", MgConst.OperLogTypes.del, "删除敏感词id：" + id);
            }
        }).start();
        return Resp.ok("删除成功");
    }

    @RequestPath("/update")
    public Resp update(final HttpRequest request) throws Exception {
        final SensitiveWordsList sensitiveWordsList = (SensitiveWordsList) JSONObject.parseObject(request.getBodyString(), SensitiveWordsList.class);
        if (sensitiveWordsList == null || sensitiveWordsList.getId() == null || sensitiveWordsList.getId().intValue() < 0) {
            return Resp.fail("无效参数");
        }
        Integer mgUid = WebUtils.currUserId(request);
        sensitiveWordsList.setMguid(mgUid);
        Ret ret = this.service.update(sensitiveWordsList);
        if (ret.isFail()) {
            return Resp.fail("操作失败");
        }
        RedissonClient redisson = RedisInit.get();
        RTopic topic = redisson.getTopic("COMMON_TOPIC");
        TopicVo topicVo = new TopicVo();
        topicVo.setType((byte) 10);
        topic.publish(topicVo);
        new Thread(new Runnable() { // from class: org.tio.mg.web.server.yanxun.word.WordController.3
            @Override // java.lang.Runnable
            public void run() {
                MgOperLogService.me.add(MgOperRequestUtils.getRequestInfo(request), "修改敏感词", (byte) 2, "敏感词：" + sensitiveWordsList.getWord());
            }
        }).start();
        return Resp.ok("修改成功");
    }

    @RequestPath("/query")
    public Resp query(HttpRequest request) throws Exception {
        RequestVo requestVo = (RequestVo) JSONObject.parseObject(request.getBodyString(), RequestVo.class);
        Ret ret = this.service.query(requestVo);
        if (ret.isFail()) {
            return Resp.fail(RetUtils.getRetMsg(ret));
        }
        return Resp.ok(RetUtils.getOkPage(ret));
    }
}
